import React from 'react'
import { Routes, Route } from 'react-router-dom'
import MainLayout from './layouts/MainLayout'
import Dashboard from './pages/Dashboard'
import DashboardMain from './pages/DashboardMain'
import DashboardNew from './pages/DashboardNew'
import SimpleDashboard from './pages/SimpleDashboard'
import TestPage from './pages/TestPage'
import BasicTest from './pages/BasicTest'
import HotspotMonitor from './pages/HotspotMonitor'
import StrategyManager from './pages/StrategyManager'
// import WalletManager from './pages/WalletManager'
import WalletManagerNew from './pages/WalletManagerNew'
// import LiquidityControl from './pages/LiquidityControl'
import LiquidityControlNew from './pages/LiquidityControlNew'
import ExitSystem from './pages/ExitSystem'
import Settings from './pages/Settings'
import BusinessFlowCenter from './components/BusinessFlowCenter'
import ResponsiveTest from './components/ResponsiveTest'
import ResponsiveTestPage from './pages/ResponsiveTestPage'
import TestNewFeatures from './pages/TestNewFeatures'
import DashboardPage from './components/DashboardPage'
import DashboardContent from './components/DashboardContent'
import StrategyContent from './components/StrategyContent'
import SettingsContent from './components/SettingsContent'
import HotspotContent from './components/HotspotContent'
import TestContent from './components/TestContent'
import SimpleDashboardContent from './components/SimpleDashboardContent'
import MinimalTestPage from './components/MinimalTestPage'
import IconTestPage from './components/IconTestPage'
import ScrollbarDemo from './pages/ScrollbarDemo'
import InteractionDemo from './components/InteractionDemo'
import NavigationTest from './components/NavigationTest'
import NotFoundPage from './components/NotFoundPage'
import ErrorBoundary from './components/ErrorBoundary'
import GlobalLayout from './components/layout/GlobalLayout'
import DashboardTestPage from './pages/DashboardTestPage'
import FullScreenDashboard from './pages/FullScreenDashboard'
import AuthPage from './pages/AuthPage'
import UserDashboard from './pages/UserDashboard'
import SubscriptionPage from './pages/SubscriptionPage'
import AdminDashboard from './pages/AdminDashboard'
import SidebarTest from './pages/SidebarTest'

function App() {
  return (
    <ErrorBoundary>
      <div className="h-full w-full flex flex-col bg-gradient-to-br from-gray-900 to-gray-800">
        <Routes>
      {/* 认证相关路由（不使用MainLayout） */}
      <Route path="/login" element={<AuthPage />} />
      <Route path="/register" element={<AuthPage />} />
      <Route path="/user-dashboard" element={<UserDashboard />} />
      <Route path="/subscription" element={<SubscriptionPage />} />
      <Route path="/admin" element={<AdminDashboard />} />

      {/* 独立测试路由（不使用MainLayout） */}
      <Route path="/basic-test" element={<BasicTest />} />
      <Route path="/fullscreen" element={<FullScreenDashboard />} />
      <Route path="/minimal-test" element={<MinimalTestPage />} />
      <Route path="/icon-test" element={<IconTestPage />} />
      <Route path="/scrollbar-demo" element={<ScrollbarDemo />} />
      <Route path="/interaction-demo" element={<InteractionDemo />} />
      <Route path="/navigation-test" element={<NavigationTest />} />
      <Route path="/sidebar-test" element={<SidebarTest />} />


      {/* 使用GlobalLayout的主要路由 */}
      <Route path="/" element={<GlobalLayout />}>
        <Route index element={<SimpleDashboardContent />} />
        <Route path="dashboard" element={<SimpleDashboardContent />} />
        <Route path="strategy" element={<StrategyContent />} />
        <Route path="settings" element={<SettingsContent />} />
        <Route path="hotspot" element={<HotspotContent />} />

        <Route path="test" element={<TestContent />} />
        <Route path="dashboard-original" element={<DashboardContent />} />
        <Route path="wallet" element={<WalletManagerNew />} />
        {/* <Route path="wallet-old" element={<WalletManager />} /> */}
        <Route path="liquidity" element={<LiquidityControlNew />} />
        {/* <Route path="liquidity-old" element={<LiquidityControl />} /> */}
        <Route path="exit" element={<ExitSystem />} />
        <Route path="business-flow" element={<BusinessFlowCenter />} />
        <Route path="responsive-test" element={<ResponsiveTestPage />} />
        <Route path="test-new-features" element={<TestNewFeatures />} />
        <Route path="dashboard-test" element={<DashboardTestPage />} />
      </Route>

      {/* 使用旧MainLayout的兼容路由 */}
      <Route path="/legacy/*" element={
        <MainLayout>
          <Routes>
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="dashboard-main" element={<DashboardMain />} />
            <Route path="dashboard-old" element={<Dashboard />} />
            <Route path="dashboard-new" element={<DashboardNew />} />
            <Route path="simple" element={<SimpleDashboard />} />
            <Route path="test" element={<TestPage />} />
            <Route path="strategy-manager" element={<StrategyManager />} />
            <Route path="settings" element={<Settings />} />
          </Routes>
        </MainLayout>
      } />

      {/* 回退路由 - 必须放在最后 */}
      <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </div>
    </ErrorBoundary>
  )
}

export default App
