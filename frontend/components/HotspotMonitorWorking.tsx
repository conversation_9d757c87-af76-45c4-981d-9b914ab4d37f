import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
  Brain, Clock, TrendingUp, Plus, Minus, RotateCcw, Filter,
  Calendar, Globe, BarChart3, Activity, Sparkles, Target,
  ChevronLeft, ChevronRight, Zap, AlertTriangle, Star
} from 'lucide-react'

// 智能时间轴操作优化组件
const HotspotMonitorWorking: React.FC = () => {
  // 基础状态
  const [activeTab, setActiveTab] = useState('prediction')
  const [activeView, setActiveView] = useState('timeline')

  // 时间轴状态
  const timelineRef = useRef<HTMLDivElement>(null)
  const [timelineZoomLevel, setTimelineZoomLevel] = useState(3)
  const [timelineViewportStart, setTimelineViewportStart] = useState(new Date(Date.now() - 24 * 60 * 60 * 1000))
  const [timelineViewportEnd, setTimelineViewportEnd] = useState(new Date(Date.now() + 24 * 60 * 60 * 1000))
  const [timelineCurrentTime] = useState(new Date())
  const [isDragging, setIsDragging] = useState(false)
  const [dragStartX, setDragStartX] = useState(0)
  const [dragStartTime, setDragStartTime] = useState(0)

  // 设备检测
  const [deviceInfo, setDeviceInfo] = useState({
    isMac: false,
    hasTouch: false,
    supportsGestures: false
  })

  // 时间轴缩放配置
  const timelineZoomConfigs = [
    { label: '年', duration: 365 * 24 * 60 * 60 * 1000, step: 30 * 24 * 60 * 60 * 1000 },
    { label: '月', duration: 30 * 24 * 60 * 60 * 1000, step: 7 * 24 * 60 * 60 * 1000 },
    { label: '周', duration: 7 * 24 * 60 * 60 * 1000, step: 24 * 60 * 60 * 1000 },
    { label: '天', duration: 24 * 60 * 60 * 1000, step: 6 * 60 * 60 * 1000 },
    { label: '12时', duration: 12 * 60 * 60 * 1000, step: 2 * 60 * 60 * 1000 },
    { label: '6时', duration: 6 * 60 * 60 * 1000, step: 60 * 60 * 1000 },
    { label: '3时', duration: 3 * 60 * 60 * 1000, step: 30 * 60 * 1000 },
    { label: '1时', duration: 60 * 60 * 1000, step: 15 * 60 * 1000 },
    { label: '30分', duration: 30 * 60 * 1000, step: 5 * 60 * 1000 },
    { label: '15分', duration: 15 * 60 * 1000, step: 2 * 60 * 1000 },
    { label: '5分', duration: 5 * 60 * 1000, step: 60 * 1000 }
  ]

  const currentTimelineConfig = timelineZoomConfigs[timelineZoomLevel] || timelineZoomConfigs[3]

  // 设备检测 - 增强版
  useEffect(() => {
    const userAgent = navigator.userAgent
    const isMac = /Mac|iPhone|iPad|iPod/.test(userAgent)

    // 更全面的触控检测
    const hasTouch = (
      'ontouchstart' in window ||
      navigator.maxTouchPoints > 0 ||
      (window as any).DocumentTouch && document instanceof (window as any).DocumentTouch ||
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
    )

    // 检测是否为触控板设备（通常在Mac/Windows笔记本上）
    const hasTouchpad = isMac || /Windows/.test(userAgent)

    const supportsGestures = 'ongesturestart' in window

    console.log('🔍 设备检测结果:', {
      userAgent,
      isMac,
      hasTouch,
      hasTouchpad,
      maxTouchPoints: navigator.maxTouchPoints,
      supportsGestures
    })

    setDeviceInfo({ isMac, hasTouch: hasTouch || hasTouchpad, supportsGestures })
  }, [])

  // 时间轴缩放函数
  const timelineZoomIn = useCallback(() => {
    if (timelineZoomLevel < timelineZoomConfigs.length - 1) {
      setTimelineZoomLevel(prev => prev + 1)
      const newConfig = timelineZoomConfigs[timelineZoomLevel + 1]
      const center = new Date((timelineViewportStart.getTime() + timelineViewportEnd.getTime()) / 2)
      const newStart = new Date(center.getTime() - newConfig.duration / 2)
      const newEnd = new Date(center.getTime() + newConfig.duration / 2)
      setTimelineViewportStart(newStart)
      setTimelineViewportEnd(newEnd)
    }
  }, [timelineZoomLevel, timelineViewportStart, timelineViewportEnd])

  const timelineZoomOut = useCallback(() => {
    if (timelineZoomLevel > 0) {
      setTimelineZoomLevel(prev => prev - 1)
      const newConfig = timelineZoomConfigs[timelineZoomLevel - 1]
      const center = new Date((timelineViewportStart.getTime() + timelineViewportEnd.getTime()) / 2)
      const newStart = new Date(center.getTime() - newConfig.duration / 2)
      const newEnd = new Date(center.getTime() + newConfig.duration / 2)
      setTimelineViewportStart(newStart)
      setTimelineViewportEnd(newEnd)
    }
  }, [timelineZoomLevel, timelineViewportStart, timelineViewportEnd])

  // 重置到当前时间
  const resetTimelineToNow = useCallback(() => {
    const now = new Date()
    const config = currentTimelineConfig
    setTimelineViewportStart(new Date(now.getTime() - config.duration / 2))
    setTimelineViewportEnd(new Date(now.getTime() + config.duration / 2))
  }, [currentTimelineConfig])

  // 时间轴平移
  const panTimeline = useCallback((deltaMs: number) => {
    setTimelineViewportStart(prev => new Date(prev.getTime() + deltaMs))
    setTimelineViewportEnd(prev => new Date(prev.getTime() + deltaMs))
  }, [])

  // 格式化时间标签
  const formatTimeLabel = useCallback((date: Date, config: any) => {
    if (config.duration >= 365 * 24 * 60 * 60 * 1000) {
      return date.getFullYear().toString()
    } else if (config.duration >= 30 * 24 * 60 * 60 * 1000) {
      return `${date.getMonth() + 1}/${date.getDate()}`
    } else if (config.duration >= 24 * 60 * 60 * 1000) {
      return `${date.getMonth() + 1}/${date.getDate()}`
    } else if (config.duration >= 60 * 60 * 1000) {
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    } else {
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }
  }, [])

  // 鼠标拖拽处理
  const handleTimelineMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 0 || e.button === 1) { // 左键或中键
      e.preventDefault()
      setIsDragging(true)
      setDragStartX(e.clientX)
      setDragStartTime(timelineViewportStart.getTime())

      if (timelineRef.current) {
        timelineRef.current.style.cursor = 'grabbing'
      }
    }
  }, [timelineViewportStart])

  // 鼠标移动处理
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      const deltaX = e.clientX - dragStartX
      const timelineWidth = timelineRef.current?.clientWidth || 1
      const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
      const deltaTime = -(deltaX / timelineWidth) * totalDuration

      const newStartTime = dragStartTime + deltaTime
      setTimelineViewportStart(new Date(newStartTime))
      setTimelineViewportEnd(new Date(newStartTime + totalDuration))
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      if (timelineRef.current) {
        timelineRef.current.style.cursor = 'grab'
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, dragStartX, dragStartTime, timelineViewportStart, timelineViewportEnd])

  // 滚轮处理 - 支持缩放和平移，智能识别触控板手势
  useEffect(() => {
    let lastWheelTime = 0
    let wheelDeltaHistory: number[] = []
    let wheelTimeHistory: number[] = []

    const handleWheel = (e: WheelEvent) => {
      if (!timelineRef.current?.contains(e.target as Node)) return

      e.preventDefault()

      const currentTime = Date.now()
      const isShiftPressed = e.shiftKey
      const isCtrlPressed = e.ctrlKey || e.metaKey

      // 记录滚轮历史用于检测触控板手势
      wheelDeltaHistory.push(e.deltaY)
      wheelTimeHistory.push(currentTime)

      // 保持历史记录在合理长度
      if (wheelDeltaHistory.length > 10) {
        wheelDeltaHistory.shift()
        wheelTimeHistory.shift()
      }

      // 检测是否为触控板双指手势
      const isTouchpadGesture = detectTouchpadGesture(wheelDeltaHistory, wheelTimeHistory, currentTime)

      console.log('滚轮事件触发:', {
        deltaY: e.deltaY,
        deltaX: e.deltaX,
        shiftKey: isShiftPressed,
        ctrlKey: isCtrlPressed,
        metaKey: e.metaKey,
        isTouchpadGesture,
        deltaMode: e.deltaMode,
        wheelDelta: (e as any).wheelDelta
      })

      // 如果检测到触控板手势且有水平分量，优先处理为平移
      if (isTouchpadGesture && Math.abs(e.deltaX) > Math.abs(e.deltaY) * 0.3) {
        // 触控板水平手势 = 平移
        const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
        const panAmount = (e.deltaX / 100) * (currentRange * 0.1)
        console.log('🖱️ 触控板水平手势平移:', {
          deltaX: e.deltaX,
          deltaY: e.deltaY,
          panAmount
        })
        panTimeline(panAmount)
      } else if (isShiftPressed) {
        // Shift + 滚轮 = 水平平移
        const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
        const panAmount = (e.deltaY / 100) * (currentRange * 0.1)
        console.log('⌨️ Shift+滚轮平移:', {
          deltaY: e.deltaY,
          panAmount
        })
        panTimeline(panAmount)
      } else if (isCtrlPressed) {
        // Ctrl/Cmd + 滚轮 = 精细缩放
        if (e.deltaY < 0) {
          timelineZoomIn()
          console.log('🔍 Ctrl+滚轮缩放：放大')
        } else {
          timelineZoomOut()
          console.log('🔍 Ctrl+滚轮缩放：缩小')
        }
      } else {
        // 普通滚轮 = 标准缩放
        if (e.deltaY < 0) {
          timelineZoomIn()
          console.log('🔍 滚轮缩放：放大')
        } else {
          timelineZoomOut()
          console.log('🔍 滚轮缩放：缩小')
        }
      }

      lastWheelTime = currentTime
    }

    // 检测触控板手势的辅助函数
    const detectTouchpadGesture = (deltaHistory: number[], timeHistory: number[], currentTime: number): boolean => {
      if (deltaHistory.length < 3) return false

      // 检查时间间隔 - 触控板通常产生更频繁的事件
      const recentEvents = timeHistory.filter(time => currentTime - time < 100)
      const isHighFrequency = recentEvents.length >= 3

      // 检查delta值的平滑性 - 触控板通常产生更平滑的值
      const recentDeltas = deltaHistory.slice(-5)
      const avgDelta = recentDeltas.reduce((sum, delta) => sum + Math.abs(delta), 0) / recentDeltas.length
      const isSmoothDelta = avgDelta < 50 && avgDelta > 1

      return isHighFrequency && isSmoothDelta
    }

    const timeline = timelineRef.current
    if (timeline) {
      timeline.addEventListener('wheel', handleWheel, { passive: false })
      console.log('滚轮事件监听器已添加')
    }

    return () => {
      if (timeline) {
        timeline.removeEventListener('wheel', handleWheel)
        console.log('滚轮事件监听器已移除')
      }
    }
  }, [timelineZoomIn, timelineZoomOut, panTimeline])

  // 触控手势支持 - 全新设计，彻底分离平移和缩放
  useEffect(() => {
    console.log('🔧 触控手势初始化:', { hasTouch: deviceInfo.hasTouch, deviceInfo })
    if (!deviceInfo.hasTouch) {
      console.log('❌ 设备不支持触控，跳过手势监听')
      return
    }

    let gestureState = {
      active: false,
      mode: null as 'pan' | 'zoom' | null,
      startTime: 0,
      startDistance: 0,
      startCenterX: 0,
      lastDistance: 0,
      lastCenterX: 0,
      distanceHistory: [] as number[],
      centerHistory: [] as number[],
      modeConfirmed: false
    }

    const handleTouchStart = (e: TouchEvent) => {
      if (!timelineRef.current?.contains(e.target as Node)) return

      if (e.touches.length === 2) {
        e.preventDefault()

        const touch1 = e.touches[0]
        const touch2 = e.touches[1]

        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
        const centerX = (touch1.clientX + touch2.clientX) / 2

        // 重置手势状态
        gestureState = {
          active: true,
          mode: null,
          startTime: Date.now(),
          startDistance: distance,
          startCenterX: centerX,
          lastDistance: distance,
          lastCenterX: centerX,
          distanceHistory: [distance],
          centerHistory: [centerX],
          modeConfirmed: false
        }

        console.log('🚀 双指触控开始:', { distance, centerX })
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!timelineRef.current?.contains(e.target as Node)) return
      if (!gestureState.active || e.touches.length !== 2) return

      e.preventDefault()

      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      const currentTime = Date.now()

      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      const currentCenterX = (touch1.clientX + touch2.clientX) / 2

      // 添加到历史记录
      gestureState.distanceHistory.push(currentDistance)
      gestureState.centerHistory.push(currentCenterX)

      // 保持历史记录在合理长度
      if (gestureState.distanceHistory.length > 10) {
        gestureState.distanceHistory.shift()
        gestureState.centerHistory.shift()
      }

      // 如果还没确定手势模式，使用历史数据分析
      if (!gestureState.modeConfirmed && currentTime - gestureState.startTime > 100) {
        const distanceVariance = calculateVariance(gestureState.distanceHistory)
        const centerVariance = calculateVariance(gestureState.centerHistory)

        // 计算总体变化趋势
        const totalDistanceChange = Math.abs(currentDistance - gestureState.startDistance)
        const totalCenterChange = Math.abs(currentCenterX - gestureState.startCenterX)

        console.log('📊 手势分析:', {
          distanceVariance: distanceVariance.toFixed(2),
          centerVariance: centerVariance.toFixed(2),
          totalDistanceChange: totalDistanceChange.toFixed(2),
          totalCenterChange: totalCenterChange.toFixed(2),
          varRatio: (distanceVariance / Math.max(centerVariance, 1)).toFixed(2)
        })

        // 使用方差分析确定主要变化方向
        if (distanceVariance > centerVariance * 3 && totalDistanceChange > 15) {
          gestureState.mode = 'zoom'
          gestureState.modeConfirmed = true
          console.log('🔍 确定为缩放模式 (方差分析)', { distanceVariance, centerVariance })
        } else if (centerVariance > distanceVariance * 2 && totalCenterChange > 10) {
          gestureState.mode = 'pan'
          gestureState.modeConfirmed = true
          console.log('👆 确定为平移模式 (方差分析)', { distanceVariance, centerVariance })
        }
      }

      // 执行对应操作 - 只有确定模式后才执行
      if (gestureState.modeConfirmed) {
        if (gestureState.mode === 'zoom') {
          const distanceChange = Math.abs(currentDistance - gestureState.lastDistance)
          if (distanceChange > 5 && currentTime - gestureState.startTime > 200) {
            if (currentDistance > gestureState.lastDistance) {
              timelineZoomIn()
              console.log('🔍 缩放：放大')
            } else {
              timelineZoomOut()
              console.log('🔍 缩放：缩小')
            }
            gestureState.lastDistance = currentDistance
            gestureState.startTime = currentTime // 重置时间防止过快操作
          }
        } else if (gestureState.mode === 'pan') {
          const centerChange = Math.abs(currentCenterX - gestureState.lastCenterX)
          if (centerChange > 2) {
            const deltaX = currentCenterX - gestureState.lastCenterX
            const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
            const panAmount = -deltaX * (currentRange / 1000)

            panTimeline(panAmount)
            console.log('👆 平移:', { deltaX: deltaX.toFixed(2), panAmount })

            gestureState.lastCenterX = currentCenterX
          }
        }
      }
    }

    // 计算方差的辅助函数
    const calculateVariance = (values: number[]): number => {
      if (values.length < 2) return 0
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      return variance
    }

    const handleTouchEnd = (e: TouchEvent) => {
      if (e.touches.length < 2) {
        console.log('🏁 双指触控结束', {
          mode: gestureState.mode,
          confirmed: gestureState.modeConfirmed,
          duration: Date.now() - gestureState.startTime
        })
        gestureState.active = false
        gestureState.mode = null
        gestureState.modeConfirmed = false
      }
    }

    const timeline = timelineRef.current
    if (timeline) {
      timeline.addEventListener('touchstart', handleTouchStart, { passive: false })
      timeline.addEventListener('touchmove', handleTouchMove, { passive: false })
      timeline.addEventListener('touchend', handleTouchEnd, { passive: false })
      console.log('✅ 触控事件监听器已添加到时间轴元素:', timeline)

      // 添加通用触控测试
      timeline.addEventListener('touchstart', (e) => {
        console.log('🖐️ 检测到触控开始:', { touches: e.touches.length, target: e.target })
      }, { passive: true })
    } else {
      console.log('❌ 时间轴元素未找到，无法添加触控监听器')
    }

    return () => {
      if (timeline) {
        timeline.removeEventListener('touchstart', handleTouchStart)
        timeline.removeEventListener('touchmove', handleTouchMove)
        timeline.removeEventListener('touchend', handleTouchEnd)
        console.log('❌ 触控事件监听器已移除')
      }
    }
  }, [deviceInfo.hasTouch, timelineZoomIn, timelineZoomOut, panTimeline, timelineViewportStart, timelineViewportEnd])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">智能时间轴操作验证</h1>
          <p className="text-gray-400">验证所有时间轴操作功能的实现效果</p>
        </div>

        {/* 智能时间轴 */}
        <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6">
          {/* 时间轴控制栏 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-400" />
                <span>智能时间轴操作验证</span>
              </h3>
              <div className="text-sm text-white/60">
                {currentTimelineConfig.label} • {formatTimeLabel(timelineViewportStart, currentTimelineConfig)} - {formatTimeLabel(timelineViewportEnd, currentTimelineConfig)}
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* 缩放控制 */}
              <div className="flex items-center space-x-1 bg-white/5 rounded-lg p-1">
                <button
                  onClick={timelineZoomOut}
                  disabled={timelineZoomLevel === 0}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  title="缩小 (或使用滚轮向下)"
                >
                  <Minus className="w-4 h-4" />
                </button>
                <span className="text-xs text-white/60 px-2 min-w-[60px] text-center">
                  {currentTimelineConfig.label}
                </span>
                <button
                  onClick={timelineZoomIn}
                  disabled={timelineZoomLevel === timelineZoomConfigs.length - 1}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  title="放大 (或使用滚轮向上)"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {/* 导航控制 */}
              <button
                onClick={resetTimelineToNow}
                className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200"
                title="回到现在"
              >
                <RotateCcw className="w-4 h-4" />
              </button>

              {/* 触控板支持指示器 */}
              {(deviceInfo.supportsGestures || deviceInfo.hasTouch) && (
                <div className="flex items-center space-x-1 text-xs text-white/50">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>{deviceInfo.isMac ? '触控板' : '触控'}</span>
                </div>
              )}
            </div>
          </div>

          {/* 操作提示 */}
          <div className="mb-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="text-sm text-blue-400 font-medium mb-2">🎯 操作指南</div>
            <div className="text-xs text-white/70 space-y-1">
              {deviceInfo.isMac ? (
                <>
                  <div>• <strong>滚轮缩放</strong>：鼠标滚轮上下滚动进行缩放</div>
                  <div>• <strong>Cmd+滚轮精细缩放</strong>：按住Cmd键+滚轮进行精细缩放</div>
                  <div>• <strong>Shift+滚轮平移</strong>：按住Shift键+滚轮进行水平平移</div>
                  <div>• <strong>双指开合缩放</strong>：触控板双指开合进行缩放（距离变化优先）</div>
                  <div>• <strong>双指移动平移</strong>：触控板双指左右移动进行平移（位置变化优先）</div>
                  <div>• <strong>拖拽/中键平移</strong>：鼠标左键或中键拖拽进行平移</div>
                </>
              ) : deviceInfo.hasTouch ? (
                <>
                  <div>• <strong>滚轮缩放</strong>：鼠标滚轮上下滚动进行缩放</div>
                  <div>• <strong>Ctrl+滚轮精细缩放</strong>：按住Ctrl键+滚轮进行精细缩放</div>
                  <div>• <strong>Shift+滚轮平移</strong>：按住Shift键+滚轮进行水平平移</div>
                  <div>• <strong>双指开合缩放</strong>：触控设备双指开合进行缩放（距离变化优先）</div>
                  <div>• <strong>双指移动平移</strong>：触控设备双指左右移动进行平移（位置变化优先）</div>
                  <div>• <strong>拖拽/中键平移</strong>：鼠标左键或中键拖拽进行平移</div>
                </>
              ) : (
                <>
                  <div>• <strong>滚轮缩放</strong>：鼠标滚轮上下滚动进行缩放</div>
                  <div>• <strong>Ctrl+滚轮精细缩放</strong>：按住Ctrl键+滚轮进行精细缩放</div>
                  <div>• <strong>Shift+滚轮平移</strong>：按住Shift键+滚轮进行水平平移</div>
                  <div>• <strong>拖拽/中键平移</strong>：鼠标左键或中键拖拽进行平移</div>
                </>
              )}
            </div>
          </div>

          {/* 时间轴主体 */}
          <div className="relative">
            {/* 时间轴容器 */}
            <div
              ref={timelineRef}
              className="relative h-96 bg-gradient-to-r from-white/5 to-white/10 rounded-lg overflow-hidden cursor-grab active:cursor-grabbing focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              onMouseDown={handleTimelineMouseDown}
              style={{ userSelect: 'none' }}
              tabIndex={0}
              title="拖拽/中键平移 • 滚轮缩放 • Ctrl+滚轮精细缩放 • Shift+滚轮平移 • 双指开合缩放 • 双指移动平移"
            >
              {/* 时间刻度线 */}
              <div className="absolute top-0 left-0 right-0 h-8 border-b border-white/20">
                {/* 动态时间标记 */}
                {[0, 25, 50, 75, 100].map((position, index) => {
                  const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
                  const timeAtPosition = new Date(timelineViewportStart.getTime() + (totalDuration * position / 100))
                  const isNow = Math.abs(timeAtPosition.getTime() - timelineCurrentTime.getTime()) < (totalDuration * 0.02)

                  return (
                    <div
                      key={index}
                      className="absolute top-0 h-full flex flex-col items-center"
                      style={{ left: `${position}%` }}
                    >
                      <div className={`w-px h-4 ${isNow ? 'bg-red-400' : 'bg-white/30'}`}></div>
                      <div className="text-xs text-white/60 mt-1">
                        {formatTimeLabel(timeAtPosition, currentTimelineConfig)}
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* 当前时间线 */}
              <div
                className="absolute top-0 bottom-0 w-px bg-red-400 shadow-lg"
                style={{
                  left: `${((timelineCurrentTime.getTime() - timelineViewportStart.getTime()) /
                    (timelineViewportEnd.getTime() - timelineViewportStart.getTime())) * 100}%`
                }}
              >
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-red-400 rounded-full shadow-lg"></div>
              </div>

              {/* 示例事件点 - 动态位置计算 */}
              {(() => {
                // 定义示例事件的固定时间点
                const event1Time = new Date(timelineCurrentTime.getTime() - 30 * 60 * 1000) // 30分钟前
                const event2Time = new Date(timelineCurrentTime.getTime() + 45 * 60 * 1000) // 45分钟后

                const viewportStart = timelineViewportStart.getTime()
                const viewportEnd = timelineViewportEnd.getTime()
                const viewportRange = viewportEnd - viewportStart

                // 计算事件在当前视口中的位置百分比
                const event1Position = ((event1Time.getTime() - viewportStart) / viewportRange) * 100
                const event2Position = ((event2Time.getTime() - viewportStart) / viewportRange) * 100

                // 只显示在视口范围内的事件
                const events = []

                if (event1Position >= -5 && event1Position <= 105) {
                  events.push(
                    <div
                      key="event1"
                      className="absolute top-16 transform -translate-x-1/2"
                      style={{ left: `${Math.max(0, Math.min(100, event1Position))}%` }}
                    >
                      <div className="w-4 h-4 bg-blue-500/20 border-2 border-blue-400 rounded-full shadow-lg hover:scale-125 transition-all duration-200 cursor-pointer">
                        <div className="absolute inset-0 rounded-full animate-ping opacity-30 bg-blue-400"></div>
                      </div>
                      <div className="absolute top-6 left-1/2 transform -translate-x-1/2 text-xs text-white/70 whitespace-nowrap">
                        示例事件 1
                      </div>
                    </div>
                  )
                }

                if (event2Position >= -5 && event2Position <= 105) {
                  events.push(
                    <div
                      key="event2"
                      className="absolute top-24 transform -translate-x-1/2"
                      style={{ left: `${Math.max(0, Math.min(100, event2Position))}%` }}
                    >
                      <div className="w-4 h-4 bg-purple-500/20 border-2 border-purple-400 rounded-full shadow-lg hover:scale-125 transition-all duration-200 cursor-pointer">
                        <div className="absolute inset-0 rounded-full animate-ping opacity-30 bg-purple-400"></div>
                      </div>
                      <div className="absolute top-6 left-1/2 transform -translate-x-1/2 text-xs text-white/70 whitespace-nowrap">
                        示例事件 2
                      </div>
                    </div>
                  )
                }

                return events
              })()}

              {/* 操作状态指示 */}
              <div className="absolute bottom-4 left-4 text-xs text-white/50">
                {isDragging ? '🖱️ 拖拽中...' : '💡 尝试各种操作方式'}
              </div>

              {/* 键盘状态指示 */}
              <div className="absolute bottom-4 right-4 text-xs text-white/50">
                <div>按住 Shift + 滚轮 = 水平平移</div>
                <div>按住 Ctrl/Cmd + 滚轮 = 精细缩放</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HotspotMonitorWorking