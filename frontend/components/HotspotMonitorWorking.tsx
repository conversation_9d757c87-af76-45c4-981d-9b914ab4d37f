import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
  Brain, Clock, TrendingUp, Plus, Minus, RotateCcw, Filter,
  Calendar, Globe, BarChart3, Activity, Sparkles, Target,
  ChevronLeft, ChevronRight, Zap, AlertTriangle, Star
} from 'lucide-react'

// 智能时间轴操作优化组件
const HotspotMonitorWorking: React.FC = () => {
  // 基础状态
  const [activeTab, setActiveTab] = useState('prediction')
  const [activeView, setActiveView] = useState('timeline')

  // 时间轴状态
  const timelineRef = useRef<HTMLDivElement>(null)
  const [timelineZoomLevel, setTimelineZoomLevel] = useState(3)
  const [timelineViewportStart, setTimelineViewportStart] = useState(new Date(Date.now() - 24 * 60 * 60 * 1000))
  const [timelineViewportEnd, setTimelineViewportEnd] = useState(new Date(Date.now() + 24 * 60 * 60 * 1000))
  const [timelineCurrentTime] = useState(new Date())
  const [isDragging, setIsDragging] = useState(false)
  const [dragStartX, setDragStartX] = useState(0)
  const [dragStartTime, setDragStartTime] = useState(0)

  // 设备检测
  const [deviceInfo, setDeviceInfo] = useState({
    isMac: false,
    hasTouch: false,
    supportsGestures: false
  })

  // 时间轴缩放配置
  const timelineZoomConfigs = [
    { label: '年', duration: 365 * 24 * 60 * 60 * 1000, step: 30 * 24 * 60 * 60 * 1000 },
    { label: '月', duration: 30 * 24 * 60 * 60 * 1000, step: 7 * 24 * 60 * 60 * 1000 },
    { label: '周', duration: 7 * 24 * 60 * 60 * 1000, step: 24 * 60 * 60 * 1000 },
    { label: '天', duration: 24 * 60 * 60 * 1000, step: 6 * 60 * 60 * 1000 },
    { label: '12时', duration: 12 * 60 * 60 * 1000, step: 2 * 60 * 60 * 1000 },
    { label: '6时', duration: 6 * 60 * 60 * 1000, step: 60 * 60 * 1000 },
    { label: '3时', duration: 3 * 60 * 60 * 1000, step: 30 * 60 * 1000 },
    { label: '1时', duration: 60 * 60 * 1000, step: 15 * 60 * 1000 },
    { label: '30分', duration: 30 * 60 * 1000, step: 5 * 60 * 1000 },
    { label: '15分', duration: 15 * 60 * 1000, step: 2 * 60 * 1000 },
    { label: '5分', duration: 5 * 60 * 1000, step: 60 * 1000 }
  ]

  const currentTimelineConfig = timelineZoomConfigs[timelineZoomLevel]

  // 设备检测
  useEffect(() => {
    const userAgent = navigator.userAgent
    const isMac = /Mac|iPhone|iPad|iPod/.test(userAgent)
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    const supportsGestures = 'ongesturestart' in window

    setDeviceInfo({ isMac, hasTouch, supportsGestures })
  }, [])

  // 时间轴缩放函数
  const timelineZoomIn = useCallback(() => {
    if (timelineZoomLevel < timelineZoomConfigs.length - 1) {
      setTimelineZoomLevel(prev => prev + 1)
      const newConfig = timelineZoomConfigs[timelineZoomLevel + 1]
      const center = new Date((timelineViewportStart.getTime() + timelineViewportEnd.getTime()) / 2)
      const newStart = new Date(center.getTime() - newConfig.duration / 2)
      const newEnd = new Date(center.getTime() + newConfig.duration / 2)
      setTimelineViewportStart(newStart)
      setTimelineViewportEnd(newEnd)
    }
  }, [timelineZoomLevel, timelineViewportStart, timelineViewportEnd])

  const timelineZoomOut = useCallback(() => {
    if (timelineZoomLevel > 0) {
      setTimelineZoomLevel(prev => prev - 1)
      const newConfig = timelineZoomConfigs[timelineZoomLevel - 1]
      const center = new Date((timelineViewportStart.getTime() + timelineViewportEnd.getTime()) / 2)
      const newStart = new Date(center.getTime() - newConfig.duration / 2)
      const newEnd = new Date(center.getTime() + newConfig.duration / 2)
      setTimelineViewportStart(newStart)
      setTimelineViewportEnd(newEnd)
    }
  }, [timelineZoomLevel, timelineViewportStart, timelineViewportEnd])

  // 重置到当前时间
  const resetTimelineToNow = useCallback(() => {
    const now = new Date()
    const config = currentTimelineConfig
    setTimelineViewportStart(new Date(now.getTime() - config.duration / 2))
    setTimelineViewportEnd(new Date(now.getTime() + config.duration / 2))
  }, [currentTimelineConfig])

  // 时间轴平移
  const panTimeline = useCallback((deltaMs: number) => {
    setTimelineViewportStart(prev => new Date(prev.getTime() + deltaMs))
    setTimelineViewportEnd(prev => new Date(prev.getTime() + deltaMs))
  }, [])

  // 格式化时间标签
  const formatTimeLabel = useCallback((date: Date, config: any) => {
    if (config.duration >= 365 * 24 * 60 * 60 * 1000) {
      return date.getFullYear().toString()
    } else if (config.duration >= 30 * 24 * 60 * 60 * 1000) {
      return `${date.getMonth() + 1}/${date.getDate()}`
    } else if (config.duration >= 24 * 60 * 60 * 1000) {
      return `${date.getMonth() + 1}/${date.getDate()}`
    } else if (config.duration >= 60 * 60 * 1000) {
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    } else {
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }
  }, [])

  // 鼠标拖拽处理
  const handleTimelineMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 0 || e.button === 1) { // 左键或中键
      e.preventDefault()
      setIsDragging(true)
      setDragStartX(e.clientX)
      setDragStartTime(timelineViewportStart.getTime())

      if (timelineRef.current) {
        timelineRef.current.style.cursor = 'grabbing'
      }
    }
  }, [timelineViewportStart])

  // 鼠标移动处理
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      const deltaX = e.clientX - dragStartX
      const timelineWidth = timelineRef.current?.clientWidth || 1
      const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
      const deltaTime = -(deltaX / timelineWidth) * totalDuration

      const newStartTime = dragStartTime + deltaTime
      setTimelineViewportStart(new Date(newStartTime))
      setTimelineViewportEnd(new Date(newStartTime + totalDuration))
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      if (timelineRef.current) {
        timelineRef.current.style.cursor = 'grab'
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, dragStartX, dragStartTime, timelineViewportStart, timelineViewportEnd])

  // 滚轮处理 - 支持缩放和平移
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (!timelineRef.current?.contains(e.target as Node)) return

      e.preventDefault()

      const isShiftPressed = e.shiftKey
      const isCtrlPressed = e.ctrlKey || e.metaKey

      console.log('滚轮事件触发:', {
        deltaY: e.deltaY,
        shiftKey: isShiftPressed,
        ctrlKey: isCtrlPressed,
        metaKey: e.metaKey
      })

      if (isShiftPressed) {
        // Shift + 滚轮 = 水平平移
        // 计算当前时间范围，根据范围调整平移速度
        const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
        const panAmount = (e.deltaY / 100) * (currentRange * 0.1) // 平移当前范围的10%
        console.log('Shift+滚轮平移:', {
          deltaY: e.deltaY,
          currentRange,
          panAmount,
          oldStart: timelineViewportStart.toISOString(),
          oldEnd: timelineViewportEnd.toISOString()
        })
        panTimeline(panAmount)
        console.log('平移后:', {
          newStart: new Date(timelineViewportStart.getTime() + panAmount).toISOString(),
          newEnd: new Date(timelineViewportEnd.getTime() + panAmount).toISOString()
        })
      } else if (isCtrlPressed) {
        // Ctrl/Cmd + 滚轮 = 精细缩放
        if (e.deltaY < 0) {
          timelineZoomIn()
        } else {
          timelineZoomOut()
        }
      } else {
        // 普通滚轮 = 标准缩放
        if (e.deltaY < 0) {
          timelineZoomIn()
        } else {
          timelineZoomOut()
        }
      }
    }

    const timeline = timelineRef.current
    if (timeline) {
      timeline.addEventListener('wheel', handleWheel, { passive: false })
      console.log('滚轮事件监听器已添加')
    }

    return () => {
      if (timeline) {
        timeline.removeEventListener('wheel', handleWheel)
        console.log('滚轮事件监听器已移除')
      }
    }
  }, [timelineZoomIn, timelineZoomOut, panTimeline])

  // 触控手势状态
  const [touchState, setTouchState] = useState({
    isActive: false,
    startDistance: 0,
    startCenterX: 0,
    lastCenterX: 0,
    gestureType: null as 'zoom' | 'pan' | null
  })

  // 触控板手势支持
  useEffect(() => {
    let touchStartTime = 0
    let initialDistance = 0
    let initialCenterX = 0
    let lastCenterX = 0
    let gestureType: 'zoom' | 'pan' | null = null

    const handleTouchStart = (e: TouchEvent) => {
      if (!timelineRef.current?.contains(e.target as Node)) return

      if (e.touches.length === 2) {
        e.preventDefault()
        touchStartTime = Date.now()

        const touch1 = e.touches[0]
        const touch2 = e.touches[1]

        // 计算初始距离和中心点
        initialDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
        initialCenterX = (touch1.clientX + touch2.clientX) / 2
        lastCenterX = initialCenterX
        gestureType = null

        console.log('双指触控开始:', { initialDistance, initialCenterX })
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!timelineRef.current?.contains(e.target as Node)) return

      if (e.touches.length === 2) {
        e.preventDefault()

        const touch1 = e.touches[0]
        const touch2 = e.touches[1]

        // 计算当前距离和中心点
        const currentDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
        const currentCenterX = (touch1.clientX + touch2.clientX) / 2

        // 计算变化量
        const distanceChange = Math.abs(currentDistance - initialDistance)
        const centerXChange = Math.abs(currentCenterX - lastCenterX)

        // 确定手势类型（只在第一次移动时确定）
        if (gestureType === null && (distanceChange > 5 || centerXChange > 5)) {
          if (distanceChange > centerXChange) {
            gestureType = 'zoom'
            console.log('检测到缩放手势')
          } else {
            gestureType = 'pan'
            console.log('检测到平移手势')
          }
        }

        // 根据手势类型执行相应操作
        if (gestureType === 'zoom') {
          // 缩放操作
          const scaleThreshold = 10 // 缩放阈值
          if (distanceChange > scaleThreshold) {
            if (currentDistance > initialDistance) {
              timelineZoomIn()
              console.log('双指缩放：放大')
            } else {
              timelineZoomOut()
              console.log('双指缩放：缩小')
            }
            initialDistance = currentDistance // 更新基准距离
          }
        } else if (gestureType === 'pan') {
          // 平移操作
          const panThreshold = 5 // 平移阈值
          const deltaX = currentCenterX - lastCenterX

          if (Math.abs(deltaX) > panThreshold) {
            const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
            const panAmount = -deltaX * (currentRange / 1000) // 转换为时间偏移

            console.log('双指平移:', { deltaX, panAmount })
            panTimeline(panAmount)
            lastCenterX = currentCenterX // 更新基准位置
          }
        }
      }
    }

    const handleTouchEnd = (e: TouchEvent) => {
      if (e.touches.length < 2) {
        gestureType = null
        console.log('双指触控结束')
      }
    }

    const timeline = timelineRef.current
    if (timeline) {
      // 只使用通用触控事件，避免与gesture事件冲突
      if (deviceInfo.hasTouch) {
        timeline.addEventListener('touchstart', handleTouchStart, { passive: false })
        timeline.addEventListener('touchmove', handleTouchMove, { passive: false })
        timeline.addEventListener('touchend', handleTouchEnd, { passive: false })
        console.log('触控事件监听器已添加')
      }
    }

    return () => {
      if (timeline) {
        if (deviceInfo.hasTouch) {
          timeline.removeEventListener('touchstart', handleTouchStart)
          timeline.removeEventListener('touchmove', handleTouchMove)
          timeline.removeEventListener('touchend', handleTouchEnd)
          console.log('触控事件监听器已移除')
        }
      }
    }
  }, [deviceInfo, timelineZoomIn, timelineZoomOut, panTimeline, timelineViewportStart, timelineViewportEnd])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">智能时间轴操作验证</h1>
          <p className="text-gray-400">验证所有时间轴操作功能的实现效果</p>
        </div>

        {/* 智能时间轴 */}
        <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6">
          {/* 时间轴控制栏 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-400" />
                <span>智能时间轴操作验证</span>
              </h3>
              <div className="text-sm text-white/60">
                {currentTimelineConfig.label} • {formatTimeLabel(timelineViewportStart, currentTimelineConfig)} - {formatTimeLabel(timelineViewportEnd, currentTimelineConfig)}
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* 缩放控制 */}
              <div className="flex items-center space-x-1 bg-white/5 rounded-lg p-1">
                <button
                  onClick={timelineZoomOut}
                  disabled={timelineZoomLevel === 0}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  title="缩小 (或使用滚轮向下)"
                >
                  <Minus className="w-4 h-4" />
                </button>
                <span className="text-xs text-white/60 px-2 min-w-[60px] text-center">
                  {currentTimelineConfig.label}
                </span>
                <button
                  onClick={timelineZoomIn}
                  disabled={timelineZoomLevel === timelineZoomConfigs.length - 1}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  title="放大 (或使用滚轮向上)"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {/* 导航控制 */}
              <button
                onClick={resetTimelineToNow}
                className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200"
                title="回到现在"
              >
                <RotateCcw className="w-4 h-4" />
              </button>

              {/* 触控板支持指示器 */}
              {(deviceInfo.supportsGestures || deviceInfo.hasTouch) && (
                <div className="flex items-center space-x-1 text-xs text-white/50">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>{deviceInfo.isMac ? '触控板' : '触控'}</span>
                </div>
              )}
            </div>
          </div>

          {/* 操作提示 */}
          <div className="mb-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="text-sm text-blue-400 font-medium mb-2">🎯 操作指南</div>
            <div className="text-xs text-white/70 space-y-1">
              {deviceInfo.isMac ? (
                <>
                  <div>• <strong>滚轮缩放</strong>：鼠标滚轮上下滚动进行缩放</div>
                  <div>• <strong>Cmd+滚轮精细缩放</strong>：按住Cmd键+滚轮进行精细缩放</div>
                  <div>• <strong>Shift+滚轮平移</strong>：按住Shift键+滚轮进行水平平移</div>
                  <div>• <strong>双指开合缩放</strong>：触控板双指开合进行缩放（距离变化优先）</div>
                  <div>• <strong>双指移动平移</strong>：触控板双指左右移动进行平移（位置变化优先）</div>
                  <div>• <strong>拖拽/中键平移</strong>：鼠标左键或中键拖拽进行平移</div>
                </>
              ) : deviceInfo.hasTouch ? (
                <>
                  <div>• <strong>滚轮缩放</strong>：鼠标滚轮上下滚动进行缩放</div>
                  <div>• <strong>Ctrl+滚轮精细缩放</strong>：按住Ctrl键+滚轮进行精细缩放</div>
                  <div>• <strong>Shift+滚轮平移</strong>：按住Shift键+滚轮进行水平平移</div>
                  <div>• <strong>双指开合缩放</strong>：触控设备双指开合进行缩放（距离变化优先）</div>
                  <div>• <strong>双指移动平移</strong>：触控设备双指左右移动进行平移（位置变化优先）</div>
                  <div>• <strong>拖拽/中键平移</strong>：鼠标左键或中键拖拽进行平移</div>
                </>
              ) : (
                <>
                  <div>• <strong>滚轮缩放</strong>：鼠标滚轮上下滚动进行缩放</div>
                  <div>• <strong>Ctrl+滚轮精细缩放</strong>：按住Ctrl键+滚轮进行精细缩放</div>
                  <div>• <strong>Shift+滚轮平移</strong>：按住Shift键+滚轮进行水平平移</div>
                  <div>• <strong>拖拽/中键平移</strong>：鼠标左键或中键拖拽进行平移</div>
                </>
              )}
            </div>
          </div>

          {/* 时间轴主体 */}
          <div className="relative">
            {/* 时间轴容器 */}
            <div
              ref={timelineRef}
              className="relative h-96 bg-gradient-to-r from-white/5 to-white/10 rounded-lg overflow-hidden cursor-grab active:cursor-grabbing focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              onMouseDown={handleTimelineMouseDown}
              style={{ userSelect: 'none' }}
              tabIndex={0}
              title="拖拽/中键平移 • 滚轮缩放 • Ctrl+滚轮精细缩放 • Shift+滚轮平移 • 双指开合缩放 • 双指移动平移"
            >
              {/* 时间刻度线 */}
              <div className="absolute top-0 left-0 right-0 h-8 border-b border-white/20">
                {/* 动态时间标记 */}
                {[0, 25, 50, 75, 100].map((position, index) => {
                  const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
                  const timeAtPosition = new Date(timelineViewportStart.getTime() + (totalDuration * position / 100))
                  const isNow = Math.abs(timeAtPosition.getTime() - timelineCurrentTime.getTime()) < (totalDuration * 0.02)

                  return (
                    <div
                      key={index}
                      className="absolute top-0 h-full flex flex-col items-center"
                      style={{ left: `${position}%` }}
                    >
                      <div className={`w-px h-4 ${isNow ? 'bg-red-400' : 'bg-white/30'}`}></div>
                      <div className="text-xs text-white/60 mt-1">
                        {formatTimeLabel(timeAtPosition, currentTimelineConfig)}
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* 当前时间线 */}
              <div
                className="absolute top-0 bottom-0 w-px bg-red-400 shadow-lg"
                style={{
                  left: `${((timelineCurrentTime.getTime() - timelineViewportStart.getTime()) /
                    (timelineViewportEnd.getTime() - timelineViewportStart.getTime())) * 100}%`
                }}
              >
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-red-400 rounded-full shadow-lg"></div>
              </div>

              {/* 示例事件点 */}
              <div className="absolute top-16 left-1/4 transform -translate-x-1/2">
                <div className="w-4 h-4 bg-blue-500/20 border-2 border-blue-400 rounded-full shadow-lg hover:scale-125 transition-all duration-200 cursor-pointer">
                  <div className="absolute inset-0 rounded-full animate-ping opacity-30 bg-blue-400"></div>
                </div>
                <div className="absolute top-6 left-1/2 transform -translate-x-1/2 text-xs text-white/70 whitespace-nowrap">
                  示例事件 1
                </div>
              </div>

              <div className="absolute top-24 left-3/4 transform -translate-x-1/2">
                <div className="w-4 h-4 bg-purple-500/20 border-2 border-purple-400 rounded-full shadow-lg hover:scale-125 transition-all duration-200 cursor-pointer">
                  <div className="absolute inset-0 rounded-full animate-ping opacity-30 bg-purple-400"></div>
                </div>
                <div className="absolute top-6 left-1/2 transform -translate-x-1/2 text-xs text-white/70 whitespace-nowrap">
                  示例事件 2
                </div>
              </div>

              {/* 操作状态指示 */}
              <div className="absolute bottom-4 left-4 text-xs text-white/50">
                {isDragging ? '🖱️ 拖拽中...' : '💡 尝试各种操作方式'}
              </div>

              {/* 键盘状态指示 */}
              <div className="absolute bottom-4 right-4 text-xs text-white/50">
                <div>按住 Shift + 滚轮 = 水平平移</div>
                <div>按住 Ctrl/Cmd + 滚轮 = 精细缩放</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HotspotMonitorWorking