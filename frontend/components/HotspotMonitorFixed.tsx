import React, { useEffect, useState, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  PieChart, Pie, Cell, Tooltip, ResponsiveContainer,
  XAxis, YAxis, CartesianGrid,
  LineChart, Line, Area, AreaChart
} from 'recharts'
import {
  Settings, Plus, X, TrendingUp, Eye, Globe, Shield,
  Brain, Video, AlertTriangle, Clock, Target, Zap,
  Filter, Languages, BarChart3, Activity, Radar, Sliders,
  RefreshCw, Users, MessageCircle, Share2, Heart,
  ExternalLink, Star, Bookmark, Calendar, Sparkles,
  Bell, CheckCircle, Key, Palette, Lock, Wallet,
  User, Save, RotateCcw, Minus, ZoomIn, ZoomOut
} from 'lucide-react'
import OptimizationPanel from './OptimizationPanel'

// 类型定义
interface HotspotData {
  id: number
  keyword: string
  title: string
  description: string
  category: string
  score: number
  trend: string
  sentiment: number
  volume: number
  sources: string[]
  timestamp: string
  region: string
  language: string
  riskLevel: string
  confidence: number
  tags: string[]
  influencers: string[]
  engagement: {
    likes: number
    shares: number
    comments: number
  }
}

interface PredictionData {
  id: string
  event_id: string
  hotspot_prob: number
  confidence: 'HIGH' | 'MEDIUM' | 'LOW'
  predicted_time: string
  strategy_ready?: boolean
  factors?: {
    temporal_score: number
    influence_score: number
    sentiment_score: number
    social_momentum: number
    media_coverage: number
    viral_potential: number
  }
  risk_assessment?: {
    market_volatility: string
    regulatory_risk: string
    competition_risk: string
    technical_risk: string
    liquidity_risk: string
  }
}

interface EventData {
  id: string
  title: string
  description: string
  event_time: string
  category: string
  key_person?: string
  impact_level: 'HIGH' | 'MEDIUM' | 'LOW'
  region: string
}

const HotspotMonitorFixed = () => {
  const navigate = useNavigate()

  // 添加气泡浮动动画样式
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      @keyframes float-0 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-10px) rotate(5deg); } }
      @keyframes float-1 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-15px) rotate(-3deg); } }
      @keyframes float-2 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-8px) rotate(7deg); } }
      @keyframes float-3 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-12px) rotate(-5deg); } }
      @keyframes float-4 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-18px) rotate(4deg); } }
      @keyframes float-5 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-6px) rotate(-8deg); } }
      @keyframes float-6 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-14px) rotate(6deg); } }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])

  // 本地状态管理，替代Redux
  const [hotspots, setHotspots] = useState<HotspotData[]>([])
  const [distribution, setDistribution] = useState({
    politics: 25,
    subculture: 20,
    technology: 18,
    metaverse: 12,
    gamefi: 8,
    nft: 6,
    sports: 11
  })
  const [sources, setSources] = useState({
    twitter: { enabled: true, weight: 0.3 },
    reddit: { enabled: true, weight: 0.25 },
    tiktok: { enabled: true, weight: 0.2 },
    youtube: { enabled: true, weight: 0.15 },
    discord: { enabled: true, weight: 0.1 }
  })
  const [settings, setSettings] = useState({
    updateInterval: 30000,
    confidenceThreshold: 0.7,
    enableDarkweb: false
  })
  const [isLoading, setIsLoading] = useState(false)
  const [mlMetrics, setMlMetrics] = useState({})
  const [sentimentIndex, setSentimentIndex] = useState(0.76)
  const [languageDistribution, setLanguageDistribution] = useState({})
  const [darkwebSignals, setDarkwebSignals] = useState([])
  const [videoAnalysis, setVideoAnalysis] = useState([])
  const [regulatoryAlerts, setRegulatoryAlerts] = useState([])
  const [predictionAccuracy, setPredictionAccuracy] = useState(78.5)

  const [newKeyword, setNewKeyword] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [activeTab, setActiveTab] = useState<'monitor' | 'prediction' | 'settings'>('monitor')
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [activeCategory, setActiveCategory] = useState('all')
  const updateInterval = useRef(null)

  // 高级筛选状态
  const [filters, setFilters] = useState({
    categories: {
      politics: true,
      subculture: true,
      technology: true,
      metaverse: true,
      gamefi: true,
      nft: true,
      sports: true
    },
    sources: {
      twitter: true,
      reddit: true,
      tiktok: true,
      youtube: true,
      discord: true,
      darkweb: false
    },
    minConfidence: 0.7,
    riskLevels: {
      low: true,
      medium: true,
      high: false
    },
    languages: {
      en: true,
      zh: true,
      ko: false,
      ja: false,
      es: false,
      ru: false
    },
    regions: {
      na: true,
      eu: true,
      ap: true,
      cn: true
    }
  })

  // 添加本地状态来存储数据
  const [localHotspots, setLocalHotspots] = useState<HotspotData[]>([])
  const [localAnalytics, setLocalAnalytics] = useState<any>(null)
  const [localLoading, setLocalLoading] = useState(true)

  // 热点预测相关状态
  const [predictions, setPredictions] = useState<PredictionData[]>([])
  const [events, setEvents] = useState<EventData[]>([])
  const [selectedEvent, setSelectedEvent] = useState<EventData | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [activeView, setActiveView] = useState('calendar')
  const [showEventModal, setShowEventModal] = useState(false)

  // 智能时间轴交互状态
  const [timelineZoomLevel, setTimelineZoomLevel] = useState(3) // 0: 年, 1: 月, 2: 周, 3: 天, 4: 小时, 5: 分钟
  const [timelineCurrentTime, setTimelineCurrentTime] = useState(new Date())
  const [timelineViewportStart, setTimelineViewportStart] = useState(new Date(Date.now() - 24 * 60 * 60 * 1000))
  const [timelineViewportEnd, setTimelineViewportEnd] = useState(new Date(Date.now() + 24 * 60 * 60 * 1000))
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState<{x: number, time: number} | null>(null)
  const [isWheelZooming, setIsWheelZooming] = useState(false)
  const [isTouchZooming, setIsTouchZooming] = useState(false)
  const [touchState, setTouchState] = useState({
    initialDistance: 0,
    initialScale: 1,
    centerPoint: null,
    lastScale: 1
  })
  const [deviceInfo, setDeviceInfo] = useState({
    isMac: false,
    hasTouch: false,
    supportsGestures: false
  })
  const timelineRef = useRef(null)

  // AI舆情监控设置状态
  const [aiSettings, setAiSettings] = useState({
    // 数据源配置
    sources: {
      twitter: { enabled: true, weight: 35 },
      reddit: { enabled: true, weight: 25 },
      tiktok: { enabled: true, weight: 20 },
      youtube: { enabled: true, weight: 15 },
      discord: { enabled: false, weight: 5 },
      darkweb: { enabled: false, weight: 10 }
    },
    // AI模型配置
    model: {
      minAdaptation: 0.75,
      predictionTarget: '70',
      captureWindow: '3-15min',
      videoAnalysis: true,
      confidenceThreshold: 0.7,
      updateInterval: 30000
    },
    // 语言设置
    languages: {
      en: true,
      zh: true,
      ko: false,
      ja: false,
      es: false,
      ru: false
    },
    // 监控范围
    monitoring: {
      keywords: ['meme', 'crypto', 'token', 'viral', 'Trump', 'Musk', 'NFT', 'GameFi'],
      regions: ['US', 'EU', 'JP', 'CN'],
      darkwebEnabled: false,
      realTimeAlerts: true
    },
    // 通知设置
    notifications: {
      hotspotAlerts: true,
      predictionUpdates: true,
      strategyReady: true,
      riskWarnings: true,
      emailNotifications: false,
      pushNotifications: true
    }
  })

  // 模拟热点数据
  const mockHotspots = [
    {
      id: 1,
      keyword: "特朗普2024竞选",
      title: "特朗普宣布2024年总统竞选计划",
      description: "前总统特朗普正式宣布参与2024年总统大选，引发全网热议",
      category: "politics",
      score: 0.95,
      trend: "rising",
      sentiment: 0.72,
      volume: 125000,
      sources: ["twitter", "reddit", "youtube"],
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      region: "na",
      language: "en",
      riskLevel: "high",
      confidence: 0.92,
      tags: ["政治", "选举", "美国"],
      influencers: ["@realDonaldTrump", "@FoxNews", "@CNN"],
      engagement: {
        likes: 45000,
        shares: 12000,
        comments: 8500
      }
    },
    {
      id: 2,
      keyword: "ChatGPT-5发布",
      title: "OpenAI即将发布ChatGPT-5",
      description: "据内部消息，OpenAI计划在下月发布ChatGPT-5，性能将大幅提升",
      category: "technology",
      score: 0.89,
      trend: "rising",
      sentiment: 0.85,
      volume: 89000,
      sources: ["twitter", "reddit", "discord"],
      timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "medium",
      confidence: 0.78,
      tags: ["AI", "科技", "OpenAI"],
      influencers: ["@sama", "@OpenAI", "@elonmusk"],
      engagement: {
        likes: 32000,
        shares: 15000,
        comments: 6800
      }
    },
    {
      id: 3,
      keyword: "元宇宙虚拟演唱会",
      title: "知名歌手在元宇宙举办虚拟演唱会",
      description: "流行歌手Taylor Swift在Horizon Worlds举办虚拟演唱会，吸引百万观众",
      category: "metaverse",
      score: 0.82,
      trend: "stable",
      sentiment: 0.91,
      volume: 67000,
      sources: ["tiktok", "youtube", "twitter"],
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "low",
      confidence: 0.85,
      tags: ["元宇宙", "音乐", "VR"],
      influencers: ["@taylorswift13", "@Meta", "@oculus"],
      engagement: {
        likes: 78000,
        shares: 25000,
        comments: 12000
      }
    },
    {
      id: 4,
      keyword: "新型GameFi项目",
      title: "革命性GameFi项目获得千万投资",
      description: "新兴GameFi项目'CryptoQuest'获得1000万美元A轮融资，游戏即将上线",
      category: "gamefi",
      score: 0.76,
      trend: "rising",
      sentiment: 0.68,
      volume: 34000,
      sources: ["discord", "reddit", "twitter"],
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "medium",
      confidence: 0.71,
      tags: ["GameFi", "投资", "区块链游戏"],
      influencers: ["@CryptoQuest", "@a16z", "@binance"],
      engagement: {
        likes: 15000,
        shares: 8000,
        comments: 3500
      }
    },
    {
      id: 5,
      keyword: "网络迷因文化",
      title: "新兴网络迷因引发病毒式传播",
      description: "一个关于AI和人类关系的迷因在各大平台疯传，引发广泛讨论",
      category: "subculture",
      score: 0.88,
      trend: "rising",
      sentiment: 0.79,
      volume: 156000,
      sources: ["tiktok", "twitter", "reddit"],
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "low",
      confidence: 0.89,
      tags: ["迷因", "文化", "病毒传播"],
      influencers: ["@pewdiepie", "@mrbeast", "@elonmusk"],
      engagement: {
        likes: 234000,
        shares: 89000,
        comments: 45000
      }
    },
    {
      id: 6,
      keyword: "NFT艺术拍卖",
      title: "数字艺术NFT创下拍卖新纪录",
      description: "知名数字艺术家的NFT作品在苏富比拍卖行以500万美元成交",
      category: "nft",
      score: 0.73,
      trend: "stable",
      sentiment: 0.65,
      volume: 28000,
      sources: ["twitter", "youtube"],
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "medium",
      confidence: 0.76,
      tags: ["NFT", "艺术", "拍卖"],
      influencers: ["@sothebys", "@beeple", "@opensea"],
      engagement: {
        likes: 18000,
        shares: 6000,
        comments: 2800
      }
    },
    {
      id: 7,
      keyword: "世界杯决赛",
      title: "世界杯决赛引发全球关注",
      description: "2024年世界杯决赛即将开始，全球球迷热情高涨",
      category: "sports",
      score: 0.94,
      trend: "rising",
      sentiment: 0.93,
      volume: 289000,
      sources: ["twitter", "tiktok", "youtube"],
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      region: "global",
      language: "multiple",
      riskLevel: "low",
      confidence: 0.96,
      tags: ["足球", "世界杯", "体育"],
      influencers: ["@FIFAcom", "@espn", "@skysports"],
      engagement: {
        likes: 456000,
        shares: 123000,
        comments: 78000
      }
    },
    {
      id: 8,
      keyword: "加密货币监管",
      title: "美国SEC发布新的加密货币监管框架",
      description: "美国证券交易委员会发布了新的加密货币监管指导方针",
      category: "politics",
      score: 0.81,
      trend: "rising",
      sentiment: 0.58,
      volume: 76000,
      sources: ["twitter", "reddit", "youtube"],
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      region: "na",
      language: "en",
      riskLevel: "high",
      confidence: 0.87,
      tags: ["监管", "加密货币", "SEC"],
      influencers: ["@SECGov", "@coinbase", "@binance"],
      engagement: {
        likes: 23000,
        shares: 11000,
        comments: 8900
      }
    }
  ]

  // 模拟预测数据
  const mockPredictions: PredictionData[] = [
    {
      id: 'pred_1',
      event_id: 'event_1',
      hotspot_prob: 0.87,
      confidence: 'HIGH',
      predicted_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      strategy_ready: true,
      factors: {
        temporal_score: 0.85,
        influence_score: 0.92,
        sentiment_score: 0.78,
        social_momentum: 0.89,
        media_coverage: 0.76,
        viral_potential: 0.91
      }
    },
    {
      id: 'pred_2',
      event_id: 'event_2',
      hotspot_prob: 0.73,
      confidence: 'MEDIUM',
      predicted_time: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      strategy_ready: false,
      factors: {
        temporal_score: 0.68,
        influence_score: 0.75,
        sentiment_score: 0.82,
        social_momentum: 0.71,
        media_coverage: 0.69,
        viral_potential: 0.77
      }
    }
  ]

  const mockEvents: EventData[] = [
    {
      id: 'event_1',
      title: 'AI监管政策发布',
      description: '预测美国将在48小时内发布新的AI监管政策',
      event_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      category: 'politics',
      key_person: 'Biden',
      impact_level: 'HIGH',
      region: 'na'
    },
    {
      id: 'event_2',
      title: '元宇宙平台整合',
      description: '预测主要元宇宙平台将宣布互操作性协议',
      event_time: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      category: 'metaverse',
      impact_level: 'MEDIUM',
      region: 'global'
    }
  ]

  // 时间轴配置
  const timelineZoomConfigs = [
    { level: 0, unit: 'year', step: 1, format: 'YYYY', label: '年视图', maxRange: 10 },
    { level: 1, unit: 'month', step: 1, format: 'YYYY-MM', label: '月视图', maxRange: 24 },
    { level: 2, unit: 'week', step: 1, format: 'MM-DD', label: '周视图', maxRange: 12 },
    { level: 3, unit: 'day', step: 1, format: 'MM-DD', label: '日视图', maxRange: 30 },
    { level: 4, unit: 'hour', step: 1, format: 'HH:mm', label: '小时视图', maxRange: 48 },
    { level: 5, unit: 'minute', step: 5, format: 'HH:mm', label: '分钟视图', maxRange: 120 }
  ]

  const currentTimelineConfig = timelineZoomConfigs[timelineZoomLevel]

  // 检测设备类型和触控板支持
  useEffect(() => {
    const detectDevice = () => {
      const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const supportsGestures = 'ongesturestart' in window

      setDeviceInfo({
        isMac,
        hasTouch,
        supportsGestures
      })
    }

    detectDevice()
  }, [])

  // 辅助函数
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 时间轴辅助函数
  const formatTimeLabel = (time: Date, config: any) => {
    switch (config.format) {
      case 'YYYY':
        return time.getFullYear().toString()
      case 'YYYY-MM':
        return `${time.getFullYear()}-${(time.getMonth() + 1).toString().padStart(2, '0')}`
      case 'MM-DD':
        return `${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')}`
      case 'HH:mm':
        return `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
      default:
        return time.toLocaleString('zh-CN')
    }
  }

  // 计算事件在时间轴上的位置
  const getTimelineEventPosition = (eventTime: string | Date) => {
    const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
    const eventOffset = new Date(eventTime).getTime() - timelineViewportStart.getTime()
    return Math.max(0, Math.min(100, (eventOffset / totalDuration) * 100))
  }

  // 触摸距离计算
  const getDistance = (touch1: any, touch2: any) => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }

  // 触摸中心点计算
  const getCenter = (touch1: any, touch2: any) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2
    }
  }

  // 缩放控制 - 支持指定中心点的缩放
  const timelineZoomIn = useCallback((centerPoint = null) => {
    if (timelineZoomLevel < timelineZoomConfigs.length - 1) {
      setTimelineZoomLevel(prev => prev + 1)
      // 缩小视口范围
      const center = centerPoint || new Date((timelineViewportStart.getTime() + timelineViewportEnd.getTime()) / 2)
      const newRange = (timelineViewportEnd.getTime() - timelineViewportStart.getTime()) / 3
      setTimelineViewportStart(new Date(center.getTime() - newRange / 2))
      setTimelineViewportEnd(new Date(center.getTime() + newRange / 2))
    }
  }, [timelineZoomLevel, timelineViewportStart, timelineViewportEnd])

  const timelineZoomOut = useCallback((centerPoint = null) => {
    if (timelineZoomLevel > 0) {
      setTimelineZoomLevel(prev => prev - 1)
      // 扩大视口范围
      const center = centerPoint || new Date((timelineViewportStart.getTime() + timelineViewportEnd.getTime()) / 2)
      const newRange = (timelineViewportEnd.getTime() - timelineViewportStart.getTime()) * 2

      // 限制最大未来时间为2年
      const maxFutureDate = new Date()
      maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 2)

      const newStart = new Date(center.getTime() - newRange / 2)
      const newEnd = new Date(Math.min(center.getTime() + newRange / 2, maxFutureDate.getTime()))

      setTimelineViewportStart(newStart)
      setTimelineViewportEnd(newEnd)
    }
  }, [timelineZoomLevel, timelineViewportStart, timelineViewportEnd])

  // 自动调整缩放级别
  const autoAdjustTimelineZoomLevel = useCallback((range: number) => {
    const rangeInMs = range

    // 根据时间范围自动选择合适的缩放级别
    if (rangeInMs <= 2 * 60 * 60 * 1000) { // 2小时以内
      setTimelineZoomLevel(5) // 分钟视图
    } else if (rangeInMs <= 48 * 60 * 60 * 1000) { // 48小时以内
      setTimelineZoomLevel(4) // 小时视图
    } else if (rangeInMs <= 30 * 24 * 60 * 60 * 1000) { // 30天以内
      setTimelineZoomLevel(3) // 日视图
    } else if (rangeInMs <= 12 * 7 * 24 * 60 * 60 * 1000) { // 12周以内
      setTimelineZoomLevel(2) // 周视图
    } else if (rangeInMs <= 24 * 30 * 24 * 60 * 60 * 1000) { // 24个月以内
      setTimelineZoomLevel(1) // 月视图
    } else {
      setTimelineZoomLevel(0) // 年视图
    }
  }, [])

  // 重置到当前时间
  const resetTimelineToNow = useCallback(() => {
    const now = new Date()
    setTimelineCurrentTime(now)

    // 根据缩放级别设置合适的视口
    const ranges = {
      0: 365 * 24 * 60 * 60 * 1000, // 年: 1年
      1: 30 * 24 * 60 * 60 * 1000,  // 月: 30天
      2: 7 * 24 * 60 * 60 * 1000,   // 周: 7天
      3: 24 * 60 * 60 * 1000,       // 日: 24小时
      4: 6 * 60 * 60 * 1000,        // 小时: 6小时
      5: 2 * 60 * 60 * 1000         // 分钟: 2小时
    }

    const range = ranges[timelineZoomLevel] || ranges[3]
    setTimelineViewportStart(new Date(now.getTime() - range / 2))
    setTimelineViewportEnd(new Date(now.getTime() + range / 2))
  }, [timelineZoomLevel])

  // 鼠标滚轮缩放处理
  const handleTimelineWheel = useCallback((e) => {
    e.preventDefault()

    // 防止过于频繁的缩放操作
    if (isWheelZooming) return

    setIsWheelZooming(true)

    // 计算鼠标在时间轴上的位置对应的时间点
    const timelineRect = timelineRef.current?.getBoundingClientRect()
    if (!timelineRect) {
      setIsWheelZooming(false)
      return
    }

    const mouseX = e.clientX - timelineRect.left
    const mousePercentage = Math.max(0, Math.min(1, mouseX / timelineRect.width))
    const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
    const mouseTime = new Date(timelineViewportStart.getTime() + totalDuration * mousePercentage)

    // 检测是否按住Ctrl键进行精细缩放
    const isFineTuning = e.ctrlKey || e.metaKey

    // 根据滚轮方向和敏感度进行缩放
    if (e.deltaY < 0) {
      // 向上滚动 - 放大
      if (isFineTuning) {
        // 精细缩放：调整视口范围而不改变缩放级别
        const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
        const newRange = currentRange * 0.9 // 缩小10%
        const centerOffset = (mouseTime.getTime() - timelineViewportStart.getTime()) / currentRange

        const newStart = new Date(mouseTime.getTime() - newRange * centerOffset)
        const newEnd = new Date(mouseTime.getTime() + newRange * (1 - centerOffset))

        setTimelineViewportStart(newStart)
        setTimelineViewportEnd(newEnd)
      } else {
        timelineZoomIn(mouseTime)
      }
    } else {
      // 向下滚动 - 缩小
      if (isFineTuning) {
        // 精细缩放：调整视口范围而不改变缩放级别
        const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
        const newRange = currentRange * 1.1 // 扩大10%
        const centerOffset = (mouseTime.getTime() - timelineViewportStart.getTime()) / currentRange

        const maxFutureDate = new Date()
        maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 2)

        const newStart = new Date(mouseTime.getTime() - newRange * centerOffset)
        const newEnd = new Date(Math.min(mouseTime.getTime() + newRange * (1 - centerOffset), maxFutureDate.getTime()))

        setTimelineViewportStart(newStart)
        setTimelineViewportEnd(newEnd)
      } else {
        timelineZoomOut(mouseTime)
      }
    }

    // 延迟重置缩放状态
    setTimeout(() => setIsWheelZooming(false), 100)
  }, [isWheelZooming, timelineViewportStart, timelineViewportEnd, timelineZoomIn, timelineZoomOut])

  // 拖拽平移
  const handleTimelineMouseDown = useCallback((e) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX, time: timelineViewportStart.getTime() })
  }, [timelineViewportStart])

  const handleTimelineMouseMove = useCallback((e) => {
    if (!isDragging || !dragStart) return

    const deltaX = e.clientX - dragStart.x
    const timelineWidth = timelineRef.current?.offsetWidth || 1000
    const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
    const timeDelta = (deltaX / timelineWidth) * totalDuration

    const newStart = new Date(dragStart.time - timeDelta)
    const newEnd = new Date(newStart.getTime() + totalDuration)

    setTimelineViewportStart(newStart)
    setTimelineViewportEnd(newEnd)
  }, [isDragging, dragStart, timelineViewportEnd, timelineViewportStart])

  const handleTimelineMouseUp = useCallback(() => {
    setIsDragging(false)
    setDragStart(null)
  }, [])

  // 执行触控缩放
  const performTimelineTouchZoom = useCallback((direction, centerTime, intensity) => {
    try {
      if (!centerTime || !direction || typeof intensity !== 'number' || intensity <= 0) {
        return
      }

      const currentRange = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
      if (currentRange <= 0) return

      // 限制缩放强度，防止过度缩放
      const clampedIntensity = Math.max(0.01, Math.min(1, intensity))
      const zoomFactor = 1 + (clampedIntensity * 2) // 调整缩放强度

      let newRange
      if (direction === 'in') {
        newRange = currentRange / zoomFactor
      } else {
        newRange = currentRange * zoomFactor
      }

      // 计算新的视口范围，以centerTime为中心
      const centerOffset = (centerTime.getTime() - timelineViewportStart.getTime()) / currentRange
      const newStart = new Date(centerTime.getTime() - newRange * centerOffset)
      const newEnd = new Date(centerTime.getTime() + newRange * (1 - centerOffset))

      // 限制最大未来时间为2年
      const maxFutureDate = new Date()
      maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 2)

      if (newEnd <= maxFutureDate && newStart < newEnd) {
        setTimelineViewportStart(newStart)
        setTimelineViewportEnd(newEnd)

        // 根据新的时间范围自动调整缩放级别
        autoAdjustTimelineZoomLevel(newRange)
      }
    } catch (error) {
      console.warn('Touch zoom error:', error)
    }
  }, [timelineViewportStart, timelineViewportEnd, autoAdjustTimelineZoomLevel])

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 只在时间轴获得焦点时响应键盘事件
      if (!timelineRef.current?.contains(document.activeElement)) return

      switch (e.key) {
        case '+':
        case '=':
          e.preventDefault()
          timelineZoomIn()
          break
        case '-':
        case '_':
          e.preventDefault()
          timelineZoomOut()
          break
        case '0':
          e.preventDefault()
          resetTimelineToNow()
          break
        case 'ArrowLeft':
          e.preventDefault()
          // 向左平移
          const leftShift = (timelineViewportEnd.getTime() - timelineViewportStart.getTime()) * 0.1
          setTimelineViewportStart(new Date(timelineViewportStart.getTime() - leftShift))
          setTimelineViewportEnd(new Date(timelineViewportEnd.getTime() - leftShift))
          break
        case 'ArrowRight':
          e.preventDefault()
          // 向右平移
          const rightShift = (timelineViewportEnd.getTime() - timelineViewportStart.getTime()) * 0.1
          setTimelineViewportStart(new Date(timelineViewportStart.getTime() + rightShift))
          setTimelineViewportEnd(new Date(timelineViewportEnd.getTime() + rightShift))
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [timelineZoomIn, timelineZoomOut, resetTimelineToNow, timelineViewportStart, timelineViewportEnd])

  // 监听鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleTimelineMouseMove)
      document.addEventListener('mouseup', handleTimelineMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleTimelineMouseMove)
        document.removeEventListener('mouseup', handleTimelineMouseUp)
      }
    }
  }, [isDragging, handleTimelineMouseMove, handleTimelineMouseUp])

  // 监听滚轮事件
  useEffect(() => {
    const timelineElement = timelineRef.current
    if (timelineElement) {
      timelineElement.addEventListener('wheel', handleTimelineWheel, { passive: false })
      return () => {
        timelineElement.removeEventListener('wheel', handleTimelineWheel)
      }
    }
  }, [handleTimelineWheel])

  const getTimeToEvent = (timeString: string) => {
    const diff = new Date(timeString).getTime() - Date.now()
    if (diff < 0) return '已过期'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}天后`
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟后`
    } else {
      return `${minutes}分钟后`
    }
  }

  const getProbabilityColor = (prob: number) => {
    if (prob >= 0.8) return 'text-red-400'
    if (prob >= 0.6) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'HIGH': return 'text-red-400 bg-red-500/10 border-red-500/30'
      case 'MEDIUM': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30'
      case 'LOW': return 'text-green-400 bg-green-500/10 border-green-500/30'
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30'
    }
  }

  const runPredictionAnalysis = async () => {
    setIsAnalyzing(true)
    // 模拟分析过程
    setTimeout(() => {
      setPredictions(mockPredictions)
      setIsAnalyzing(false)
    }, 2000)
  }

  // 设置处理函数
  const handleSourceToggle = (source: string) => {
    setAiSettings(prev => ({
      ...prev,
      sources: {
        ...prev.sources,
        [source]: {
          ...prev.sources[source],
          enabled: !prev.sources[source].enabled
        }
      }
    }))
  }

  const handleSourceWeightChange = (source: string, weight: number) => {
    setAiSettings(prev => ({
      ...prev,
      sources: {
        ...prev.sources,
        [source]: {
          ...prev.sources[source],
          weight
        }
      }
    }))
  }

  const handleModelSettingChange = (key: string, value: any) => {
    setAiSettings(prev => ({
      ...prev,
      model: {
        ...prev.model,
        [key]: value
      }
    }))
  }

  const handleLanguageToggle = (language: string) => {
    setAiSettings(prev => ({
      ...prev,
      languages: {
        ...prev.languages,
        [language]: !prev.languages[language]
      }
    }))
  }

  const handleNotificationToggle = (key: string) => {
    setAiSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: !prev.notifications[key]
      }
    }))
  }

  const handleKeywordAdd = (keyword: string) => {
    if (keyword && !aiSettings.monitoring.keywords.includes(keyword)) {
      setAiSettings(prev => ({
        ...prev,
        monitoring: {
          ...prev.monitoring,
          keywords: [...prev.monitoring.keywords, keyword]
        }
      }))
    }
  }

  const handleKeywordRemove = (keyword: string) => {
    setAiSettings(prev => ({
      ...prev,
      monitoring: {
        ...prev.monitoring,
        keywords: prev.monitoring.keywords.filter(k => k !== keyword)
      }
    }))
  }

  const saveSettings = () => {
    // 模拟保存设置
    console.log('保存设置:', aiSettings)
    // 这里可以调用API保存设置
  }

  const resetSettings = () => {
    // 重置为默认设置
    setAiSettings({
      sources: {
        twitter: { enabled: true, weight: 35 },
        reddit: { enabled: true, weight: 25 },
        tiktok: { enabled: true, weight: 20 },
        youtube: { enabled: true, weight: 15 },
        discord: { enabled: false, weight: 5 },
        darkweb: { enabled: false, weight: 10 }
      },
      model: {
        minAdaptation: 0.75,
        predictionTarget: '70',
        captureWindow: '3-15min',
        videoAnalysis: true,
        confidenceThreshold: 0.7,
        updateInterval: 30000
      },
      languages: {
        en: true,
        zh: true,
        ko: false,
        ja: false,
        es: false,
        ru: false
      },
      monitoring: {
        keywords: ['meme', 'crypto', 'token', 'viral', 'Trump', 'Musk', 'NFT', 'GameFi'],
        regions: ['US', 'EU', 'JP', 'CN'],
        darkwebEnabled: false,
        realTimeAlerts: true
      },
      notifications: {
        hotspotAlerts: true,
        predictionUpdates: true,
        strategyReady: true,
        riskWarnings: true,
        emailNotifications: false,
        pushNotifications: true
      }
    })
  }

  // 筛选处理函数
  const handleFilterChange = (category: string, key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const handleResetFilters = () => {
    setFilters({
      categories: {
        politics: true,
        subculture: true,
        technology: true,
        metaverse: true,
        gamefi: true,
        nft: true,
        sports: true
      },
      sources: {
        twitter: true,
        reddit: true,
        tiktok: true,
        youtube: true,
        discord: true,
        darkweb: false
      },
      minConfidence: 0.7,
      riskLevels: {
        low: true,
        medium: true,
        high: false
      },
      languages: {
        en: true,
        zh: true,
        ko: false,
        ja: false,
        es: false,
        ru: false
      },
      regions: {
        na: true,
        eu: true,
        ap: true,
        cn: true
      }
    })
  }

  const applyFilters = () => {
    // 应用筛选逻辑
    console.log('应用筛选:', filters)
    // 这里可以触发数据重新筛选
    setShowAdvancedFilters(false)
  }

  // 根据筛选条件过滤热点数据
  const getFilteredHotspots = () => {
    return localHotspots.filter(hotspot => {
      // 分类筛选
      if (!filters.categories[hotspot.category]) return false

      // 置信度筛选
      if (hotspot.confidence < filters.minConfidence) return false

      // 风险等级筛选
      if (!filters.riskLevels[hotspot.riskLevel]) return false

      // 语言筛选
      if (!filters.languages[hotspot.language]) return false

      // 地区筛选 - 修复地区映射
      const regionMapping = {
        'na': 'na',
        'global': 'na', // 将global映射到na
        'eu': 'eu',
        'ap': 'ap',
        'cn': 'cn'
      }
      const mappedRegion = regionMapping[hotspot.region] || 'na'
      if (!filters.regions[mappedRegion]) return false

      return true
    })
  }

  // 初始化数据
  useEffect(() => {
    console.log('🚀 开始初始化热点数据...')
    setLocalLoading(true)
    // 模拟加载延迟
    setTimeout(() => {
      console.log('📊 设置热点数据:', mockHotspots.length, '个热点')
      setLocalHotspots(mockHotspots)
      setHotspots(mockHotspots)
      setPredictions(mockPredictions)
      setEvents(mockEvents)
      setLocalLoading(false)
      setIsLoading(false)
      console.log('✅ 热点数据初始化完成')
    }, 1000)
  }, [])

  // 调试：监听localHotspots变化
  useEffect(() => {
    console.log('📈 localHotspots 更新:', localHotspots.length, '个热点')
    if (localHotspots.length > 0) {
      console.log('🔍 第一个热点:', localHotspots[0])
    }
  }, [localHotspots])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 flex flex-col">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-orange-500/10 text-orange-400 rounded-xl">
              <Brain className="text-2xl" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">趋势热点监控</h1>
              <p className="text-gray-400 text-lg">实时监测全球热点趋势，AI驱动的智能预测</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 bg-green-500/10 text-green-400 px-4 py-2 rounded-xl">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>实时监控</span>
            </div>
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl px-4 py-2">
              <span className="text-gray-400">预测准确率: </span>
              <span className="text-green-400 font-bold">{predictionAccuracy}%</span>
            </div>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 mb-8 bg-gray-800/40 backdrop-blur-sm border border-gray-700/50 p-1 rounded-xl shadow-lg">
          <button
            onClick={() => setActiveTab('monitor')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'monitor'
                ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg transform scale-[1.02]'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
            }`}
          >
            <Brain className="w-5 h-5" />
            <span>实时热点</span>
          </button>
          <button
            onClick={() => setActiveTab('prediction')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'prediction'
                ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg transform scale-[1.02]'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
            }`}
          >
            <Brain className="w-5 h-5" />
            <span>热点预测</span>
          </button>

          <button
            onClick={() => setActiveTab('settings')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'settings'
                ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transform scale-[1.02]'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
            }`}
          >
            <Settings className="w-5 h-5" />
            <span>参数设置</span>
          </button>
        </div>

        {/* 实时热点标签页内容 */}
        {activeTab === 'monitor' && (
          <div className="min-h-[800px]">
            {/* 加载状态 */}
            {localLoading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-center">
                  <div className="w-16 h-16 border-4 border-orange-500/30 border-t-orange-500 rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-400">正在加载热点数据...</p>
                </div>
              </div>
            ) : (
              <div className="space-y-8">
                {/* 热点统计概览 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-red-500/10 to-orange-500/10 border border-red-500/20 rounded-2xl p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-red-400 text-sm font-medium">超高热度</p>
                        <p className="text-3xl font-bold text-white mt-1">
                          {localHotspots.filter(h => h.score >= 0.9).length}
                        </p>
                      </div>
                      <div className="p-3 bg-red-500/20 rounded-xl">
                        <TrendingUp className="w-8 h-8 text-red-400" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-orange-500/10 to-yellow-500/10 border border-orange-500/20 rounded-2xl p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-orange-400 text-sm font-medium">高热度</p>
                        <p className="text-3xl font-bold text-white mt-1">
                          {localHotspots.filter(h => h.score >= 0.8 && h.score < 0.9).length}
                        </p>
                      </div>
                      <div className="p-3 bg-orange-500/20 rounded-xl">
                        <Zap className="w-8 h-8 text-orange-400" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-400 text-sm font-medium">总讨论量</p>
                        <p className="text-3xl font-bold text-white mt-1">
                          {(localHotspots.reduce((sum, h) => sum + h.volume, 0) / 1000).toFixed(0)}K
                        </p>
                      </div>
                      <div className="p-3 bg-blue-500/20 rounded-xl">
                        <Activity className="w-8 h-8 text-blue-400" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-400 text-sm font-medium">平均置信度</p>
                        <p className="text-3xl font-bold text-white mt-1">
                          {localHotspots.length > 0 ? Math.round(localHotspots.reduce((sum, h) => sum + h.confidence, 0) / localHotspots.length * 100) : 0}%
                        </p>
                      </div>
                      <div className="p-3 bg-green-500/20 rounded-xl">
                        <Shield className="w-8 h-8 text-green-400" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 筛选和搜索栏 */}
                <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div className="flex items-center space-x-4">
                      <h2 className="text-xl font-bold text-white">热点筛选</h2>
                      <div className="flex items-center space-x-2">
                        <Filter className="w-4 h-4 text-gray-400" />
                        <select
                          value={activeCategory}
                          onChange={(e) => setActiveCategory(e.target.value)}
                          className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm"
                        >
                          <option value="all">全部分类</option>
                          <option value="politics">政治</option>
                          <option value="technology">科技</option>
                          <option value="subculture">亚文化</option>
                          <option value="metaverse">元宇宙</option>
                          <option value="gamefi">GameFi</option>
                          <option value="nft">NFT</option>
                          <option value="sports">体育</option>
                        </select>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <select
                          value={selectedTimeRange}
                          onChange={(e) => setSelectedTimeRange(e.target.value)}
                          className="bg-transparent text-white text-sm border-none outline-none"
                        >
                          <option value="1h">最近1小时</option>
                          <option value="6h">最近6小时</option>
                          <option value="24h">最近24小时</option>
                          <option value="7d">最近7天</option>
                        </select>
                      </div>

                      <button
                        onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                          showAdvancedFilters
                            ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                            : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
                        }`}
                      >
                        <Filter className="w-4 h-4" />
                        <span>高级筛选</span>
                      </button>

                      <button className="flex items-center space-x-2 bg-orange-500/20 text-orange-400 px-4 py-2 rounded-lg hover:bg-orange-500/30 transition-colors">
                        <RefreshCw className="w-4 h-4" />
                        <span>刷新</span>
                      </button>

                      <button
                        onClick={() => setActiveTab('settings')}
                        className="flex items-center space-x-2 bg-blue-500/20 text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500/30 transition-colors"
                      >
                        <Settings className="w-4 h-4" />
                        <span>参数设置</span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* 高级筛选面板 */}
                {showAdvancedFilters && (
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 border-l-4 border-blue-500">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                        <Filter className="w-5 h-5 text-blue-400" />
                        <span>高级筛选器</span>
                      </h3>
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={handleResetFilters}
                          className="flex items-center space-x-2 bg-gray-500/20 text-gray-400 px-3 py-2 rounded-lg hover:bg-gray-500/30 transition-colors text-sm"
                        >
                          <RotateCcw className="w-4 h-4" />
                          <span>重置</span>
                        </button>
                        <button
                          onClick={applyFilters}
                          className="flex items-center space-x-2 bg-blue-500/20 text-blue-400 px-3 py-2 rounded-lg hover:bg-blue-500/30 transition-colors text-sm"
                        >
                          <CheckCircle className="w-4 h-4" />
                          <span>应用筛选</span>
                        </button>
                        <button
                          onClick={() => setShowAdvancedFilters(false)}
                          className="text-gray-400 hover:text-white transition-colors"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {/* 分类筛选 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <label className="block text-white/70 text-sm mb-3 font-medium">分类筛选</label>
                        <div className="space-y-2">
                          {[
                            { id: 'politics', name: '政治', color: 'red', count: 12 },
                            { id: 'subculture', name: '亚文化', color: 'purple', count: 8 },
                            { id: 'technology', name: '科技', color: 'blue', count: 15 },
                            { id: 'metaverse', name: '元宇宙', color: 'cyan', count: 6 },
                            { id: 'gamefi', name: 'GameFi', color: 'green', count: 9 },
                            { id: 'nft', name: 'NFT', color: 'pink', count: 4 },
                            { id: 'sports', name: '体育', color: 'yellow', count: 7 }
                          ].map((category) => (
                            <label key={category.id} className="flex items-center space-x-3 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={filters.categories[category.id]}
                                onChange={(e) => handleFilterChange('categories', category.id, e.target.checked)}
                                className="w-4 h-4 text-blue-600 rounded"
                              />
                              <div className={`w-3 h-3 rounded-full bg-${category.color}-400`}></div>
                              <span className="text-white text-sm flex-1">{category.name}</span>
                              <span className="text-white/50 text-xs">{category.count}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* 数据源筛选 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <label className="block text-white/70 text-sm mb-3 font-medium">数据源</label>
                        <div className="space-y-2">
                          {[
                            { key: 'twitter', name: 'Twitter', icon: '🐦' },
                            { key: 'reddit', name: 'Reddit', icon: '🤖' },
                            { key: 'tiktok', name: 'TikTok', icon: '🎵' },
                            { key: 'youtube', name: 'YouTube', icon: '📺' },
                            { key: 'discord', name: 'Discord', icon: '💬' },
                            { key: 'darkweb', name: 'Darkweb', icon: '🕸️' }
                          ].map((source) => (
                            <label key={source.key} className="flex items-center space-x-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={filters.sources[source.key]}
                                onChange={(e) => handleFilterChange('sources', source.key, e.target.checked)}
                                className="w-4 h-4 text-blue-600 rounded"
                              />
                              <span className="text-sm">{source.icon}</span>
                              <span className="text-white text-sm">{source.name}</span>
                            </label>
                          ))}
                        </div>

                        {/* 置信度阈值 */}
                        <div className="mt-4">
                          <label className="block text-white/70 text-sm mb-2">
                            最小置信度: {Math.round(filters.minConfidence * 100)}%
                          </label>
                          <input
                            type="range"
                            min="0.5"
                            max="1"
                            step="0.05"
                            value={filters.minConfidence}
                            onChange={(e) => setFilters(prev => ({ ...prev, minConfidence: parseFloat(e.target.value) }))}
                            className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                          />
                        </div>
                      </div>

                      {/* 风险等级和其他筛选 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        {/* 风险等级 */}
                        <div className="mb-4">
                          <label className="block text-white/70 text-sm mb-2 font-medium">风险等级</label>
                          <div className="space-y-2">
                            {[
                              { key: 'low', name: '低风险', color: 'green' },
                              { key: 'medium', name: '中风险', color: 'yellow' },
                              { key: 'high', name: '高风险', color: 'red' }
                            ].map((risk) => (
                              <label key={risk.key} className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={filters.riskLevels[risk.key]}
                                  onChange={(e) => handleFilterChange('riskLevels', risk.key, e.target.checked)}
                                  className="w-4 h-4 text-blue-600 rounded"
                                />
                                <span className={`text-sm text-${risk.color}-400`}>{risk.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* 语言筛选 */}
                        <div className="mb-4">
                          <label className="block text-white/70 text-sm mb-2 font-medium">语言</label>
                          <div className="space-y-2">
                            {[
                              { code: 'en', name: '英语', flag: '🇺🇸' },
                              { code: 'zh', name: '中文', flag: '🇨🇳' },
                              { code: 'ko', name: '韩语', flag: '🇰🇷' },
                              { code: 'ja', name: '日语', flag: '🇯🇵' }
                            ].map((lang) => (
                              <label key={lang.code} className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={filters.languages[lang.code]}
                                  onChange={(e) => handleFilterChange('languages', lang.code, e.target.checked)}
                                  className="w-4 h-4 text-blue-600 rounded"
                                />
                                <span className="text-sm">{lang.flag}</span>
                                <span className="text-white text-sm">{lang.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* 地区筛选 */}
                        <div>
                          <label className="block text-white/70 text-sm mb-2 font-medium">地区</label>
                          <div className="space-y-2">
                            {[
                              { id: 'na', name: '北美', flag: '🇺🇸' },
                              { id: 'eu', name: '欧洲', flag: '🇪🇺' },
                              { id: 'ap', name: '亚太', flag: '🌏' },
                              { id: 'cn', name: '中国', flag: '🇨🇳' }
                            ].map((region) => (
                              <label key={region.id} className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={filters.regions[region.id]}
                                  onChange={(e) => handleFilterChange('regions', region.id, e.target.checked)}
                                  className="w-4 h-4 text-blue-600 rounded"
                                />
                                <span className="text-sm">{region.flag}</span>
                                <span className="text-white text-sm">{region.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 热点列表 - 卡片网格布局 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {getFilteredHotspots()
                    .filter(hotspot => activeCategory === 'all' || hotspot.category === activeCategory)
                    .map((hotspot) => (
                      <div key={hotspot.id} className="group bg-gradient-to-br from-gray-800/60 to-gray-900/60 border border-gray-700/50 rounded-2xl p-6 hover:border-orange-500/30 hover:shadow-2xl hover:shadow-orange-500/10 transition-all duration-300">
                      {/* 热点头部信息 */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className={`relative p-2 rounded-xl ${
                            hotspot.score >= 0.9 ? 'bg-red-500/20 text-red-400' :
                            hotspot.score >= 0.8 ? 'bg-orange-500/20 text-orange-400' :
                            hotspot.score >= 0.7 ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-green-500/20 text-green-400'
                          }`}>
                            <TrendingUp className="w-5 h-5" />
                            {hotspot.score >= 0.9 && (
                              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                            )}
                          </div>

                          <div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 rounded-lg text-xs font-bold ${
                                hotspot.score >= 0.9 ? 'text-red-400 bg-red-500/10 border border-red-500/20' :
                                hotspot.score >= 0.8 ? 'text-orange-400 bg-orange-500/10 border border-orange-500/20' :
                                hotspot.score >= 0.7 ? 'text-yellow-400 bg-yellow-500/10 border border-yellow-500/20' :
                                'text-green-400 bg-green-500/10 border border-green-500/20'
                              }`}>
                                {Math.round(hotspot.score * 100)}
                              </span>
                              <span className="text-2xl">
                                {hotspot.trend === 'rising' ? '🚀' : hotspot.trend === 'falling' ? '📉' : '➡️'}
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(hotspot.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-lg text-xs font-medium ${
                            hotspot.category === 'politics' ? 'bg-red-500/10 text-red-400' :
                            hotspot.category === 'technology' ? 'bg-blue-500/10 text-blue-400' :
                            hotspot.category === 'subculture' ? 'bg-purple-500/10 text-purple-400' :
                            hotspot.category === 'metaverse' ? 'bg-cyan-500/10 text-cyan-400' :
                            hotspot.category === 'gamefi' ? 'bg-green-500/10 text-green-400' :
                            hotspot.category === 'nft' ? 'bg-pink-500/10 text-pink-400' :
                            'bg-yellow-500/10 text-yellow-400'
                          }`}>
                            {hotspot.category}
                          </span>

                          <button className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-orange-400 transition-all">
                            <Bookmark className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      {/* 热点内容 */}
                      <div className="mb-4">
                        <h3 className="text-lg font-bold text-white mb-2 line-clamp-2 group-hover:text-orange-400 transition-colors">
                          {hotspot.title}
                        </h3>
                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                          {hotspot.description}
                        </p>
                      </div>

                      {/* 标签 */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {hotspot.tags?.slice(0, 3).map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-lg">
                            #{tag}
                          </span>
                        ))}
                      </div>

                      {/* 统计信息 */}
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-blue-400">
                            <Eye className="w-4 h-4" />
                            <span className="text-sm font-medium">{(hotspot.volume / 1000).toFixed(1)}K</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">讨论量</p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-green-400">
                            <Shield className="w-4 h-4" />
                            <span className="text-sm font-medium">{Math.round(hotspot.confidence * 100)}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">置信度</p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-purple-400">
                            <Heart className="w-4 h-4" />
                            <span className="text-sm font-medium">{Math.round(hotspot.sentiment * 100)}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">情绪指数</p>
                        </div>
                      </div>

                      {/* 互动数据 */}
                      {hotspot.engagement && (
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                          <div className="flex items-center space-x-1">
                            <Heart className="w-3 h-3" />
                            <span>{(hotspot.engagement.likes / 1000).toFixed(1)}K</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Share2 className="w-3 h-3" />
                            <span>{(hotspot.engagement.shares / 1000).toFixed(1)}K</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MessageCircle className="w-3 h-3" />
                            <span>{(hotspot.engagement.comments / 1000).toFixed(1)}K</span>
                          </div>
                        </div>
                      )}

                      {/* 操作按钮 */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                        <div className="flex items-center space-x-2">
                          {hotspot.sources?.slice(0, 3).map((source, index) => (
                            <span key={index} className="px-2 py-1 bg-gray-700/30 text-gray-400 text-xs rounded">
                              {source}
                            </span>
                          ))}
                        </div>

                        <div className="flex items-center space-x-2">
                          <button className="flex items-center space-x-1 px-3 py-1.5 bg-orange-500/20 text-orange-400 rounded-lg hover:bg-orange-500/30 transition-colors text-xs font-medium">
                            <Zap className="w-3 h-3" />
                            <span>生成策略</span>
                          </button>

                          <button className="p-1.5 text-gray-400 hover:text-white transition-colors">
                            <ExternalLink className="w-4 h-4" />
                          </button>
                        </div>
                        </div>
                      </div>
                    ))}
                </div>

                {/* 空状态 */}
                {getFilteredHotspots().filter(hotspot => activeCategory === 'all' || hotspot.category === activeCategory).length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Brain className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">暂无热点数据</h3>
                    <p className="text-gray-400 text-sm">当前筛选条件下没有找到相关热点，请尝试调整筛选条件</p>
                  </div>
                )}

                {/* 热点分析面板 */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 热点趋势图 */}
                  <div className="lg:col-span-2 bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-bold text-white">热点趋势分析</h3>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1 text-green-400">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-sm">上升</span>
                        </div>
                        <div className="flex items-center space-x-1 text-red-400">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          <span className="text-sm">下降</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {['政治', '科技', '亚文化', '元宇宙'].map((category, index) => {
                        const percentage = [85, 72, 68, 45][index];
                        const isRising = [true, true, false, true][index];
                        return (
                          <div key={category} className="flex items-center space-x-4">
                            <div className="w-16 text-sm text-gray-400">{category}</div>
                            <div className="flex-1 bg-gray-700/50 rounded-full h-3 overflow-hidden">
                              <div
                                className={`h-full rounded-full transition-all duration-1000 ${
                                  isRising ? 'bg-gradient-to-r from-green-500 to-emerald-400' : 'bg-gradient-to-r from-red-500 to-orange-400'
                                }`}
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                            <div className="w-12 text-sm text-white font-medium">{percentage}%</div>
                            <div className="w-6">
                              {isRising ? (
                                <TrendingUp className="w-4 h-4 text-green-400" />
                              ) : (
                                <TrendingUp className="w-4 h-4 text-red-400 rotate-180" />
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 实时统计 */}
                  <div className="space-y-6">
                    <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                      <h3 className="text-lg font-bold text-white mb-4">实时统计</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">活跃热点</span>
                          <span className="text-2xl font-bold text-orange-400">{localHotspots.length}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">总讨论量</span>
                          <span className="text-2xl font-bold text-blue-400">
                            {(localHotspots.reduce((sum, h) => sum + h.volume, 0) / 1000000).toFixed(1)}M
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">平均热度</span>
                          <span className="text-2xl font-bold text-green-400">
                            {localHotspots.length > 0 ? Math.round(localHotspots.reduce((sum, h) => sum + h.score, 0) / localHotspots.length * 100) : 0}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                      <h3 className="text-lg font-bold text-white mb-4">热点来源</h3>
                      <div className="space-y-3">
                        {[
                          { name: 'Twitter', count: 45, color: 'bg-blue-500' },
                          { name: 'Reddit', count: 32, color: 'bg-orange-500' },
                          { name: 'TikTok', count: 28, color: 'bg-pink-500' },
                          { name: 'YouTube', count: 15, color: 'bg-red-500' }
                        ].map((source) => (
                          <div key={source.name} className="flex items-center space-x-3">
                            <div className={`w-3 h-3 ${source.color} rounded-full`}></div>
                            <span className="text-gray-400 flex-1">{source.name}</span>
                            <span className="text-white font-medium">{source.count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 热点预测标签页内容 */}
        {activeTab === 'prediction' && (
          <div className="min-h-[800px] space-y-6">
            {/* 标题和控制栏 */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                  <Brain className="w-5 h-5 text-purple-400" />
                  <span>热点预测与策略自动化</span>
                </h3>
                <p className="text-white/60 text-sm mt-1">
                  AI驱动的热点预测 • ML模型分析 • 智能策略生成
                </p>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={runPredictionAnalysis}
                  disabled={isAnalyzing}
                  className="flex items-center space-x-2 bg-purple-500/20 text-purple-400 px-4 py-2 rounded-lg hover:bg-purple-500/30 transition-colors disabled:opacity-50"
                >
                  {isAnalyzing ? (
                    <Activity className="w-4 h-4 animate-pulse" />
                  ) : (
                    <Sparkles className="w-4 h-4" />
                  )}
                  <span>{isAnalyzing ? '分析中...' : 'AI预测分析'}</span>
                </button>

                <button className="flex items-center space-x-2 bg-orange-500/20 text-orange-400 px-4 py-2 rounded-lg hover:bg-orange-500/30 transition-colors">
                  <Plus className="w-4 h-4" />
                  <span>添加事件</span>
                </button>
              </div>
            </div>

            {/* 视图切换 */}
            <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
              {[
                { id: 'calendar', name: '智能日历', icon: Calendar },
                { id: 'timeline', name: '时间轴视图', icon: Clock },
                { id: 'predictions', name: '智能分析', icon: TrendingUp },
                { id: 'global', name: '全球监控', icon: Globe },
                { id: 'analytics', name: '深度分析', icon: BarChart3 }
              ].map((view) => (
                <button
                  key={view.id}
                  onClick={() => setActiveView(view.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
                    activeView === view.id
                      ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <view.icon className="w-4 h-4" />
                  <span>{view.name}</span>
                </button>
              ))}
            </div>

            {/* 预测概览统计 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="bg-gradient-to-br from-purple-500/10 to-indigo-500/10 border border-purple-500/20 rounded-2xl p-6">
                <div className="flex items-center space-x-3">
                  <Brain className="w-8 h-8 text-purple-400" />
                  <div>
                    <div className="text-white font-medium">预测事件</div>
                    <div className="text-purple-400 text-2xl font-bold">{predictions.length}</div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-6">
                <div className="flex items-center space-x-3">
                  <Sparkles className="w-8 h-8 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">策略就绪</div>
                    <div className="text-yellow-400 text-2xl font-bold">
                      {predictions.filter(p => p.strategy_ready).length}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-2xl p-6">
                <div className="flex items-center space-x-3">
                  <Target className="w-8 h-8 text-blue-400" />
                  <div>
                    <div className="text-white font-medium">高置信度</div>
                    <div className="text-blue-400 text-2xl font-bold">
                      {predictions.filter(p => p.confidence === 'HIGH').length}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 智能时间轴 */}
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6">
              {/* 时间轴控制栏 */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                    <Clock className="w-5 h-5 text-blue-400" />
                    <span>智能时间轴</span>
                  </h3>
                  <div className="text-sm text-white/60">
                    {currentTimelineConfig.label} • {formatTimeLabel(timelineViewportStart, currentTimelineConfig)} - {formatTimeLabel(timelineViewportEnd, currentTimelineConfig)}
                  </div>
                  <div className="text-xs text-white/40 mt-1">
                    {deviceInfo.isMac ? (
                      <>💡 滚轮缩放 • Cmd+滚轮精细缩放 • 双指开合缩放 • 拖拽平移 • +/- 键缩放 • 0 键回到现在 • ←→ 键平移</>
                    ) : deviceInfo.hasTouch ? (
                      <>💡 滚轮缩放 • Ctrl+滚轮精细缩放 • 双指触控缩放 • 拖拽平移 • +/- 键缩放 • 0 键回到现在 • ←→ 键平移</>
                    ) : (
                      <>💡 滚轮缩放 • Ctrl+滚轮精细缩放 • 拖拽平移 • +/- 键缩放 • 0 键回到现在 • ←→ 键平移</>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {/* 缩放控制 */}
                  <div className="flex items-center space-x-1 bg-white/5 rounded-lg p-1">
                    <button
                      onClick={() => timelineZoomOut()}
                      disabled={timelineZoomLevel === 0}
                      className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      title="缩小 (或使用滚轮向下)"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="text-xs text-white/60 px-2 min-w-[60px] text-center">
                      {currentTimelineConfig.label}
                    </span>
                    <button
                      onClick={() => timelineZoomIn()}
                      disabled={timelineZoomLevel === timelineZoomConfigs.length - 1}
                      className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      title="放大 (或使用滚轮向上)"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>

                  {/* 导航控制 */}
                  <button
                    onClick={resetTimelineToNow}
                    className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200"
                    title="回到现在"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>

                  {/* 触控板支持指示器 */}
                  {(deviceInfo.supportsGestures || deviceInfo.hasTouch) && (
                    <div className="flex items-center space-x-1 text-xs text-white/50">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span>{deviceInfo.isMac ? '触控板' : '触控'}</span>
                    </div>
                  )}

                  {/* 添加事件 */}
                  <button className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span className="text-sm">添加事件</span>
                  </button>
                </div>
              </div>

              {/* 时间轴主体 */}
              <div className="relative">
                {/* 时间轴容器 */}
                <div
                  ref={timelineRef}
                  className="relative h-96 bg-gradient-to-r from-white/5 to-white/10 rounded-lg overflow-hidden cursor-grab active:cursor-grabbing focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                  onMouseDown={handleTimelineMouseDown}
                  style={{ userSelect: 'none' }}
                  tabIndex={0}
                  title="拖拽平移 • 滚轮缩放 • Ctrl+滚轮精细缩放 • 双指开合缩放 • 键盘快捷键: +/- 缩放, 0 回到现在, ←→ 平移"
                >
                  {/* 时间刻度线 */}
                  <div className="absolute top-0 left-0 right-0 h-8 border-b border-white/20">
                    {/* 动态时间标记 */}
                    {[0, 25, 50, 75, 100].map((position, index) => {
                      const totalDuration = timelineViewportEnd.getTime() - timelineViewportStart.getTime()
                      const timeAtPosition = new Date(timelineViewportStart.getTime() + (totalDuration * position / 100))
                      const isNow = Math.abs(timeAtPosition.getTime() - timelineCurrentTime.getTime()) < (totalDuration * 0.02)

                      return (
                        <div
                          key={index}
                          className="absolute top-0 h-full flex flex-col items-center"
                          style={{ left: `${position}%` }}
                        >
                          <div className={`w-px h-4 ${isNow ? 'bg-red-400' : 'bg-white/30'}`}></div>
                          <div className="text-xs text-white/60 mt-1">
                            {formatTimeLabel(timeAtPosition, currentTimelineConfig)}
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  {/* 当前时间线 */}
                  <div
                    className="absolute top-0 bottom-0 w-px bg-red-400 shadow-lg"
                    style={{ left: `${getTimelineEventPosition(timelineCurrentTime)}%` }}
                  >
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-red-400 rounded-full shadow-lg"></div>
                  </div>

                  {/* 预测事件轨道 */}
                  <div className="relative h-20 border-b border-white/10 mt-8">
                    <div className="absolute left-2 top-2 text-xs text-purple-400 font-medium">
                      AI预测事件
                    </div>
                    {predictions.map((prediction, index) => {
                      const event = events.find(e => e.id === prediction.event_id);
                      const position = getTimelineEventPosition(prediction.predicted_time);
                      const isVisible = position >= 0 && position <= 100;

                      if (!isVisible) return null;

                      return (
                        <div
                          key={`prediction-${index}`}
                          className="absolute top-6 transform -translate-x-1/2 cursor-pointer group"
                          style={{ left: `${position}%` }}
                        >
                          {/* 预测事件点 */}
                          <div className={`w-4 h-4 rounded-full border-2 shadow-lg group-hover:scale-125 transition-all duration-200 relative ${
                            prediction.confidence === 'HIGH' ? 'bg-red-500/20 border-red-400' :
                            prediction.confidence === 'MEDIUM' ? 'bg-yellow-500/20 border-yellow-400' :
                            'bg-green-500/20 border-green-400'
                          }`}>
                            <div className="absolute inset-0 rounded-full animate-ping opacity-30"
                                 style={{ backgroundColor: prediction.confidence === 'HIGH' ? '#ef4444' :
                                                          prediction.confidence === 'MEDIUM' ? '#f59e0b' : '#10b981' }}>
                            </div>
                          </div>

                          {/* 事件标签 */}
                          <div className="absolute top-7 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100
                            transition-all duration-200 bg-black/90 text-white text-xs p-2 rounded whitespace-nowrap z-30 border border-white/20">
                            <div className="font-medium">{event?.title || `预测事件 ${index + 1}`}</div>
                            <div className="text-white/70">置信度: {prediction.confidence}</div>
                            <div className="text-white/70">概率: {(prediction.hotspot_prob * 100).toFixed(1)}%</div>
                            <div className="text-white/70">
                              {new Date(prediction.predicted_time).toLocaleString('zh-CN')}
                            </div>
                          </div>

                          {/* 连接线 */}
                          <div className="absolute top-4 left-1/2 w-px h-8 bg-white/20 transform -translate-x-1/2"></div>
                        </div>
                      );
                    })}
                  </div>

                  {/* 实际事件轨道 */}
                  <div className="relative h-20 border-b border-white/10">
                    <div className="absolute left-2 top-2 text-xs text-blue-400 font-medium">
                      实际事件
                    </div>
                    {events.map((event, index) => {
                      const position = getTimelineEventPosition(event.event_time);
                      const isVisible = position >= 0 && position <= 100;
                      const prediction = predictions.find(p => p.event_id === event.id);

                      if (!isVisible) return null;

                      return (
                        <div
                          key={`event-${index}`}
                          className="absolute top-6 transform -translate-x-1/2 cursor-pointer group"
                          style={{ left: `${position}%` }}
                        >
                          {/* 事件点 */}
                          <div className={`w-5 h-5 rounded-full border-2 shadow-lg group-hover:scale-125 transition-all duration-200 flex items-center justify-center ${
                            event.impact_level === 'HIGH' ? 'bg-red-500/20 border-red-400' :
                            event.impact_level === 'MEDIUM' ? 'bg-yellow-500/20 border-yellow-400' :
                            'bg-blue-500/20 border-blue-400'
                          }`}>
                            {event.impact_level === 'HIGH' && <Star className="w-2 h-2 text-white" />}
                            {prediction?.confidence === 'HIGH' && <AlertTriangle className="w-2 h-2 text-white" />}
                          </div>

                          {/* 事件标签 */}
                          <div className="absolute top-7 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100
                            transition-all duration-200 bg-black/90 text-white text-xs p-2 rounded whitespace-nowrap z-30 border border-white/20">
                            <div className="font-medium">{event.title}</div>
                            <div className="text-white/70">{event.description}</div>
                            <div className="text-white/70">
                              {new Date(event.event_time).toLocaleString('zh-CN')}
                            </div>
                            {event.key_person && (
                              <div className="text-blue-400">关键人物: {event.key_person}</div>
                            )}
                          </div>

                          {/* 连接线 */}
                          <div className="absolute top-5 left-1/2 w-px h-8 bg-white/20 transform -translate-x-1/2"></div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* 时间轴统计信息 */}
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <div className="text-white/70 text-xs">总事件</div>
                    <div className="text-white text-lg font-bold">{events.length}</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <div className="text-white/70 text-xs">AI预测</div>
                    <div className="text-purple-400 text-lg font-bold">{predictions.length}</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <div className="text-white/70 text-xs">高置信度</div>
                    <div className="text-red-400 text-lg font-bold">
                      {predictions.filter(p => p.confidence === 'HIGH').length}
                    </div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <div className="text-white/70 text-xs">策略就绪</div>
                    <div className="text-green-400 text-lg font-bold">
                      {predictions.filter(p => p.strategy_ready).length}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 智能日历视图 */}
            {activeView === 'calendar' && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* 事件列表 */}
                <div className="lg:col-span-2 bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                  <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-blue-400" />
                    <span>事件时间线</span>
                  </h4>

                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {events.map((event, index) => {
                      const prediction = predictions.find(p => p.event_id === event.id)

                      return (
                        <div
                          key={event.id}
                          className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                          onClick={() => setSelectedEvent(event)}
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h5 className="text-white font-medium">{event.title}</h5>
                              <p className="text-white/60 text-sm mt-1">{event.description}</p>
                            </div>

                            <div className="text-right ml-4">
                              <div className="text-white/70 text-sm">{formatTime(event.event_time)}</div>
                              <div className="text-xs text-white/50 mt-1">{getTimeToEvent(event.event_time)}</div>
                            </div>
                          </div>

                          {prediction && (
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <span className="text-white/70 text-sm">预测概率:</span>
                                <span className={`font-bold ${getProbabilityColor(prediction.hotspot_prob)}`}>
                                  {(prediction.hotspot_prob * 100).toFixed(1)}%
                                </span>
                              </div>

                              <div className={`px-2 py-1 rounded text-xs border ${getConfidenceColor(prediction.confidence)}`}>
                                {prediction.confidence}
                              </div>
                            </div>
                          )}

                          {event.key_person && (
                            <div className="mt-2 flex items-center space-x-2">
                              <span className="text-white/50 text-xs">关键人物:</span>
                              <span className="text-blue-400 text-xs">{event.key_person}</span>
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* 预测统计 */}
                <div className="space-y-6">
                  {/* 预测概览 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5 text-green-400" />
                      <span>预测概览</span>
                    </h4>

                    <div className="space-y-4">
                      <div className="bg-red-500/10 rounded-lg p-3 border border-red-500/30">
                        <div className="flex items-center justify-between">
                          <span className="text-red-400 text-sm">高概率事件</span>
                          <span className="text-red-400 font-bold">
                            {predictions.filter(p => p.confidence === 'HIGH').length}
                          </span>
                        </div>
                      </div>

                      <div className="bg-yellow-500/10 rounded-lg p-3 border border-yellow-500/30">
                        <div className="flex items-center justify-between">
                          <span className="text-yellow-400 text-sm">中等概率事件</span>
                          <span className="text-yellow-400 font-bold">
                            {predictions.filter(p => p.confidence === 'MEDIUM').length}
                          </span>
                        </div>
                      </div>

                      <div className="bg-green-500/10 rounded-lg p-3 border border-green-500/30">
                        <div className="flex items-center justify-between">
                          <span className="text-green-400 text-sm">低概率事件</span>
                          <span className="text-green-400 font-bold">
                            {predictions.filter(p => p.confidence === 'LOW').length}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 智能提醒 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Bell className="w-5 h-5 text-yellow-400" />
                      <span>智能提醒</span>
                    </h4>

                    <div className="space-y-3">
                      {predictions
                        .filter(p => p.confidence === 'HIGH')
                        .slice(0, 3)
                        .map((prediction, index) => {
                          const event = events.find(e => e.id === prediction.event_id)
                          return (
                            <div key={index} className="bg-white/5 rounded-lg p-3">
                              <div className="flex items-start space-x-2">
                                <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5" />
                                <div className="flex-1">
                                  <div className="text-white text-sm font-medium">
                                    {event?.title}
                                  </div>
                                  <div className="text-white/60 text-xs mt-1">
                                    预计 {getTimeToEvent(prediction.predicted_time)} 爆发
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        })}
                    </div>
                  </div>

                  {/* 快速操作 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-purple-400" />
                      <span>快速操作</span>
                    </h4>

                    <div className="space-y-2">
                      <button className="w-full bg-purple-500/20 text-purple-400 py-2 px-4 rounded-lg hover:bg-purple-500/30 transition-colors text-sm">
                        生成策略草稿
                      </button>
                      <button className="w-full bg-orange-500/20 text-orange-400 py-2 px-4 rounded-lg hover:bg-orange-500/30 transition-colors text-sm">
                        设置提醒
                      </button>
                      <button className="w-full bg-blue-500/20 text-blue-400 py-2 px-4 rounded-lg hover:bg-blue-500/30 transition-colors text-sm">
                        导出日历
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 智能分析视图 */}
            {activeView === 'predictions' && (
              <div className="space-y-6">
                {/* 预测事件分析区域 */}
                <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="text-white font-medium flex items-center space-x-2">
                      <Brain className="w-5 h-5 text-purple-400" />
                      <span>未来事件预测分析</span>
                    </h4>
                    <div className="text-white/60 text-sm">
                      基于AI模型的事件预测和策略建议
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {predictions.map((prediction, index) => {
                      const event = events.find(e => e.id === prediction.event_id)

                      return (
                        <div key={index} className="bg-white/5 rounded-lg p-5 border border-white/10 hover:bg-white/10 transition-all duration-200">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <h5 className="text-white font-medium">{event?.title}</h5>
                              <p className="text-white/60 text-sm mt-1">{event?.description}</p>
                            </div>
                            <div className={`px-3 py-1 rounded text-sm border ${getConfidenceColor(prediction.confidence)}`}>
                              {prediction.confidence}
                            </div>
                          </div>

                          <div className="space-y-4">
                            {/* 预测概率 */}
                            <div className="bg-white/5 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-3">
                                <span className="text-white/70 text-sm">热点爆发概率</span>
                                <span className={`font-bold text-xl ${getProbabilityColor(prediction.hotspot_prob)}`}>
                                  {(prediction.hotspot_prob * 100).toFixed(1)}%
                                </span>
                              </div>
                              <div className="w-full bg-white/20 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full transition-all duration-300 ${
                                    prediction.hotspot_prob >= 0.8 ? 'bg-red-400' :
                                    prediction.hotspot_prob >= 0.6 ? 'bg-yellow-400' : 'bg-green-400'
                                  }`}
                                  style={{ width: `${prediction.hotspot_prob * 100}%` }}
                                ></div>
                              </div>

                              {/* 策略建议 */}
                              {prediction.strategy_ready && (
                                <div className="mt-3 p-3 bg-green-500/10 border border-green-500/30 rounded">
                                  <div className="flex items-center space-x-2">
                                    <CheckCircle className="w-4 h-4 text-green-400" />
                                    <span className="text-green-400 text-sm font-medium">策略就绪</span>
                                  </div>
                                  <p className="text-white/70 text-xs mt-1">
                                    AI已生成完整发币策略，可立即部署
                                  </p>
                                </div>
                              )}
                            </div>

                            {/* 预测因子分析 */}
                            <div className="bg-white/5 rounded-lg p-4">
                              <h6 className="text-white font-medium mb-3 text-sm">AI预测因子</h6>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-white/70 text-sm">时间序列分析</span>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-16 bg-white/20 rounded-full h-1.5">
                                      <div
                                        className="h-1.5 bg-blue-400 rounded-full"
                                        style={{ width: `${(prediction.factors?.temporal_score || 0) * 100}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-blue-400 text-sm font-medium w-10">
                                      {((prediction.factors?.temporal_score || 0) * 100).toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-white/70 text-sm">影响力评估</span>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-16 bg-white/20 rounded-full h-1.5">
                                      <div
                                        className="h-1.5 bg-green-400 rounded-full"
                                        style={{ width: `${(prediction.factors?.influence_score || 0) * 100}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-green-400 text-sm font-medium w-10">
                                      {((prediction.factors?.influence_score || 0) * 100).toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-white/70 text-sm">情感分析</span>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-16 bg-white/20 rounded-full h-1.5">
                                      <div
                                        className="h-1.5 bg-purple-400 rounded-full"
                                        style={{ width: `${(prediction.factors?.sentiment_score || 0) * 100}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-purple-400 text-sm font-medium w-10">
                                      {((prediction.factors?.sentiment_score || 0) * 100).toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* 时间和操作 */}
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="text-white/70 text-xs">预测峰值时间</div>
                                <div className="text-white text-sm font-medium">{formatTime(prediction.predicted_time)}</div>
                              </div>

                              {prediction.strategy_ready && (
                                <button className="bg-orange-500/20 text-orange-400 text-xs py-2 px-4 rounded-lg hover:bg-orange-500/30 transition-colors flex items-center space-x-2">
                                  <Sparkles className="w-3 h-3" />
                                  <span>生成策略</span>
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* 设置标签页内容 */}
            {activeTab === 'settings' && (
              <div className="min-h-[800px] space-y-6">
                {/* 设置标题和操作栏 */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                      <Settings className="w-5 h-5 text-blue-400" />
                      <span>AI舆情监控设置</span>
                    </h3>
                    <p className="text-white/60 text-sm mt-1">
                      配置数据源、AI模型参数和监控范围
                    </p>
                  </div>

                  <div className="flex items-center space-x-3">
                    <button
                      onClick={resetSettings}
                      className="flex items-center space-x-2 bg-gray-500/20 text-gray-400 px-4 py-2 rounded-lg hover:bg-gray-500/30 transition-colors"
                    >
                      <RotateCcw className="w-4 h-4" />
                      <span>重置</span>
                    </button>

                    <button
                      onClick={saveSettings}
                      className="flex items-center space-x-2 bg-blue-500/20 text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500/30 transition-colors"
                    >
                      <Save className="w-4 h-4" />
                      <span>保存设置</span>
                    </button>
                  </div>
                </div>

                {/* 设置内容网格 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">

                  {/* 数据源配置 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Globe className="w-5 h-5 text-green-400" />
                      <span>数据源配置</span>
                    </h4>

                    <div className="space-y-4">
                      {Object.entries(aiSettings.sources).map(([source, config]) => (
                        <div key={source} className="bg-white/5 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={config.enabled}
                                onChange={() => handleSourceToggle(source)}
                                className="w-4 h-4 text-blue-600 rounded"
                              />
                              <span className="text-white font-medium capitalize">{source}</span>
                            </div>
                            <span className="text-white/60 text-sm">{config.weight}%</span>
                          </div>

                          {config.enabled && (
                            <div>
                              <label className="block text-white/70 text-sm mb-2">
                                权重: {config.weight}%
                              </label>
                              <input
                                type="range"
                                min="5"
                                max="50"
                                value={config.weight}
                                onChange={(e) => handleSourceWeightChange(source, parseInt(e.target.value))}
                                className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                              />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* AI模型配置 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Brain className="w-5 h-5 text-purple-400" />
                      <span>AI模型配置</span>
                    </h4>

                    <div className="space-y-4">
                      {/* 适配度阈值 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <label className="block text-white/70 text-sm mb-2">
                          适配度阈值: {aiSettings.model.minAdaptation.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0.5"
                          max="1"
                          step="0.01"
                          value={aiSettings.model.minAdaptation}
                          onChange={(e) => handleModelSettingChange('minAdaptation', parseFloat(e.target.value))}
                          className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="text-xs text-white/50 mt-1">误差要求: &lt;0.1</div>
                      </div>

                      {/* 预测准确率目标 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <label className="block text-white/70 text-sm mb-2">预测准确率目标</label>
                        <select
                          value={aiSettings.model.predictionTarget}
                          onChange={(e) => handleModelSettingChange('predictionTarget', e.target.value)}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                        >
                          <option value="65">65% (最低要求)</option>
                          <option value="70">70% (推荐)</option>
                          <option value="75">75% (高精度)</option>
                          <option value="80">80% (极高精度)</option>
                        </select>
                      </div>

                      {/* 热点捕捉窗口 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <label className="block text-white/70 text-sm mb-2">热点捕捉窗口</label>
                        <select
                          value={aiSettings.model.captureWindow}
                          onChange={(e) => handleModelSettingChange('captureWindow', e.target.value)}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                        >
                          <option value="1-5min">1-5分钟 (超早期)</option>
                          <option value="3-15min">3-15分钟 (萌芽阶段)</option>
                          <option value="5-30min">5-30分钟 (成长期)</option>
                          <option value="10-60min">10-60分钟 (成熟期)</option>
                        </select>
                      </div>

                      {/* 视频内容分析 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">视频内容分析</span>
                          <input
                            type="checkbox"
                            checked={aiSettings.model.videoAnalysis}
                            onChange={(e) => handleModelSettingChange('videoAnalysis', e.target.checked)}
                            className="w-4 h-4 text-purple-600 rounded"
                          />
                        </div>
                        <div className="text-xs text-white/50 mt-1">PyTorch OCR + 关键帧识别</div>
                      </div>
                    </div>
                  </div>

                  {/* 语言和地区设置 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Languages className="w-5 h-5 text-yellow-400" />
                      <span>语言配置</span>
                    </h4>

                    <div className="space-y-3">
                      {[
                        { code: 'en', name: '英语', flag: '🇺🇸' },
                        { code: 'zh', name: '中文', flag: '🇨🇳' },
                        { code: 'ko', name: '韩语', flag: '🇰🇷' },
                        { code: 'ja', name: '日语', flag: '🇯🇵' },
                        { code: 'es', name: '西班牙语', flag: '🇪🇸' },
                        { code: 'ru', name: '俄语', flag: '🇷🇺' }
                      ].map((lang) => (
                        <div key={lang.code} className="bg-white/5 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <span className="text-lg">{lang.flag}</span>
                              <span className="text-white text-sm">{lang.name}</span>
                            </div>
                            <input
                              type="checkbox"
                              checked={aiSettings.languages[lang.code]}
                              onChange={() => handleLanguageToggle(lang.code)}
                              className="w-4 h-4 text-yellow-600 rounded"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 监控范围配置 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Target className="w-5 h-5 text-orange-400" />
                      <span>监控范围</span>
                    </h4>

                    <div className="space-y-4">
                      {/* 关键词管理 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <label className="block text-white/70 text-sm mb-3">监控关键词</label>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {aiSettings.monitoring.keywords.map((keyword, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center space-x-1 bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs"
                            >
                              <span>{keyword}</span>
                              <button
                                onClick={() => handleKeywordRemove(keyword)}
                                className="text-blue-400 hover:text-red-400"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </span>
                          ))}
                        </div>
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            placeholder="添加关键词..."
                            className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                handleKeywordAdd(e.target.value)
                                e.target.value = ''
                              }
                            }}
                          />
                          <button
                            onClick={() => {
                              const input = document.querySelector('input[placeholder="添加关键词..."]') as HTMLInputElement
                              if (input?.value) {
                                handleKeywordAdd(input.value)
                                input.value = ''
                              }
                            }}
                            className="bg-blue-500/20 text-blue-400 px-3 py-2 rounded-lg hover:bg-blue-500/30 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      {/* 暗网监控 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-white/70 text-sm">暗网监控</span>
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={aiSettings.monitoring.darkwebEnabled}
                              onChange={(e) => setAiSettings(prev => ({
                                ...prev,
                                monitoring: {
                                  ...prev.monitoring,
                                  darkwebEnabled: e.target.checked
                                }
                              }))}
                              className="w-4 h-4 text-red-600 rounded"
                            />
                            <Shield className="w-4 h-4 text-red-400" />
                          </div>
                        </div>
                        <div className="text-xs text-white/50">
                          ⚠️ 暗网监控涉及敏感内容，请确保合规使用
                        </div>
                      </div>

                      {/* 实时警报 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">实时警报</span>
                          <input
                            type="checkbox"
                            checked={aiSettings.monitoring.realTimeAlerts}
                            onChange={(e) => setAiSettings(prev => ({
                              ...prev,
                              monitoring: {
                                ...prev.monitoring,
                                realTimeAlerts: e.target.checked
                              }
                            }))}
                            className="w-4 h-4 text-green-600 rounded"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 通知设置 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Bell className="w-5 h-5 text-indigo-400" />
                      <span>通知设置</span>
                    </h4>

                    <div className="space-y-3">
                      {[
                        { key: 'hotspotAlerts', label: '热点警报', desc: '新热点检测时通知' },
                        { key: 'predictionUpdates', label: '预测更新', desc: '预测结果变化时通知' },
                        { key: 'strategyReady', label: '策略就绪', desc: 'AI策略生成完成时通知' },
                        { key: 'riskWarnings', label: '风险警告', desc: '检测到高风险事件时通知' },
                        { key: 'emailNotifications', label: '邮件通知', desc: '通过邮件发送通知' },
                        { key: 'pushNotifications', label: '推送通知', desc: '浏览器推送通知' }
                      ].map((notification) => (
                        <div key={notification.key} className="bg-white/5 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-white text-sm font-medium">{notification.label}</span>
                            <input
                              type="checkbox"
                              checked={aiSettings.notifications[notification.key]}
                              onChange={() => handleNotificationToggle(notification.key)}
                              className="w-4 h-4 text-indigo-600 rounded"
                            />
                          </div>
                          <div className="text-xs text-white/50">{notification.desc}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default HotspotMonitorFixed
